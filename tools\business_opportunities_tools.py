from typing import Optional, Any
import json
from agno.tools import Toolkit
from agno.utils.log import logger
import requests
from utils.error_notice import sendErrorNotice
from utils.context import get_context
import os
from dotenv import load_dotenv
load_dotenv()

def get_business(bo_id: int) -> str:
    """获取商机详细信息"""
    try:
        user_id = get_context("user_id")
        response = requests.get(
            f"{os.getenv('API_URL')}/pyapi/busiopp/detail?bo_id={bo_id}&user_id={user_id}")

        if response.status_code == 200:
            res = response.json()
            return json.dumps(res, ensure_ascii=False)
        else:
            logger.error(f"获取商机详细信息失败: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": "获取商机详细信息失败"
            }, ensure_ascii=False)

    except Exception as e:
        logger.error(f"获取商机信息失败: {str(e)}")
        return json.dumps({
            "status": "error",
            "message": f"获取商机信息失败: {str(e)}"
        }, ensure_ascii=False)

class BusinessOpportunitiesTools(Toolkit):
    """销售商机管理工具类"""

    def __init__(self):
        super().__init__(name="sales_business_tools")

        # 注册工具函数
        self.register(self.add_business)
        self.register(self.update_business)
        self.register(self.bind_customer)
        self.register(get_business)
        self.register(self.list_business)
        self.register(self.delete_business)
        self.register(self.sort_business)
        self.register(self.add_business_interact)

    def add_business(self,
                     customer_id: int,
                     title: str,
                     content:  Optional[str] = None,
                     source:  Optional[str] = None,
                     status: Optional[int] = None,
                     budget: Optional[int] = None,
                     notes: Optional[str] = None
                     ) -> str:
        """
        添加新的商机

        Args:
            customer_id: 客户ID
            title: 商机标题（通常格式为，公司简称+需求的产品或服务简称）
            content: 需求内容：客户需求的产品或者服务
            source: 来源
            status: 当前阶段,(1，初步接触，2，需求确认， 3，竞品分析，4，商务谈判，5，赢单关闭，6，丢单关闭) 默认是1
            budget: （可选）预算,报价，价格，预期成交(元)默认为0，不要编造金额
            notes: 商机备注（可选填：沟通记录、背景信息、关键细节等内容）

             - 根据已有的信息，能提取到title就可以，其他没有提取到参数对应的值，留空就可以,不要再向用户提问，也不要编造数据

        Returns:
            str: JSON格式的响应结果,包含商机ID 和链接
        """
        try:
            data: dict[str, Any] = {
                "customer_id": customer_id,
                "title": title,
                "content": content,
                "source": source,
                "status": status,
                "budget": budget,
                "notes": notes,
                "user_id": get_context("user_id")
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/busiopp/add", data=data)

            if response.status_code == 200:
                res = response.json()
                return json.dumps(res, ensure_ascii=False)

        except Exception as e:
            logger.error(f"添加销售商机失败: {str(e)}")
            return str({"status": "error", "message": f"添加销售商机失败: {str(e)}"})

    def update_business(self,
                        bo_id: int,
                        title:  Optional[str] = None,
                        content:  Optional[str] = None,
                        source:  Optional[str] = None,
                        status: Optional[int] = None,
                        budget: Optional[int] = None,
                        customer_id: Optional[int] = None,
                        notes: Optional[str] = None
                        ) -> str:
        """更新商机信息

        Args:
            bo_id: 商机ID (没有的话需要调用list_business工具获取)
            title: 商机标题（通常格式为，公司简称+需求的产品或服务简称）
            content: 需求内容：客户需求的产品或者服务
            source: 来源
            status: 当前阶段,(1，初步接触，2，需求确认， 3，竞品分析，4，商务谈判，5，赢单关闭，6，丢单关闭)
            budget: 预算,报价，价格，预期成交(元)
            customer_id: 客户ID
            notes: 商机备注（可选填：沟通记录、背景信息、关键细节等内容）
        """

        try:
            data: dict[str, Any] = {
                "bo_id": bo_id,
                "title": title,
                "content": content,
                "source": source,
                "status": status,
                "budget": budget,
                "customer_id": customer_id,
                "notes": notes,
                "user_id": get_context("user_id")
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/busiopp/edit", data=data)

            if response.status_code == 200:
                res = response.json()
                return json.dumps(res, ensure_ascii=False)
            else:
                logger.error(f"更新商机信息失败: {response.text}")
                return json.dumps({
                    "status": "error",
                    "message": "更新商机信息失败"
                }, ensure_ascii=False)
        except Exception as e:
            logger.error(f"更新商机信息失败: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"更新商机信息失败: {str(e)}"
            }, ensure_ascii=False)

    def bind_customer(self, bo_id: int, customer_id: int) -> str:
        """将商机与客户关联

        Args:
            bo_id: 商机ID (没有的话需要调用list_business工具获取)
            customer_id: 客户ID

        """

        try:
            data: dict[str, Any] = {
                "bo_id": bo_id,
                "customer_id": customer_id,
                "user_id": get_context("user_id")
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/busiopp/bindCustomer", data=data)

            if response.status_code == 200:
                res = response.json()
                return json.dumps(res, ensure_ascii=False)
            else:
                return json.dumps({
                    "status": "error",
                    "message": f"将商机与客户关联失败,{response.text}"
                }, ensure_ascii=False)
        except Exception as e:
            logger.error(f"将商机与客户关联失败: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"将商机与客户关联失败: {str(e)}"
            }, ensure_ascii=False)

    def list_business(self,) -> str:
        """查询所有商机列表信息"""
        try:
            user_id = get_context("user_id")
            response = requests.get(
                f"{os.getenv('API_URL')}/pyapi/busiopp/list?user_id={user_id}")
            logger.info(response.text)
            if response.status_code == 200:
                res = response.json()
                return json.dumps(res, ensure_ascii=False)
            else:
                return json.dumps({
                    "status": "error",
                    "message": "查询商机列表失败"
                }, ensure_ascii=False)

        except Exception as e:
            logger.error(f"查询所有商机列表失败: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"查询所有商机列表失败: {str(e)}"
            }, ensure_ascii=False)

    def delete_business(self,
                        bo_id: int | str,
                        ) -> str:
        """删除商机，支持批量删除
        Args:
            bo_id: 商机ID，有多个id时用,间隔

        Returns:
            str: 包含操作结果的JSON字符串
        """
        try:
            data: dict[str, Any] = {
                "bo_id": bo_id,
                "user_id": get_context("user_id")
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/busiopp/del", data=data)

            if response.status_code == 200:
                res = response.json()
                return json.dumps(res, ensure_ascii=False)
            else:
                logger.error(f"删除商机失败: {response.text}")
                return json.dumps({
                    "status": "error",
                    "message": "删除商机失败"
                }, ensure_ascii=False)
        except Exception as e:
            logger.error(f"删除商机失败: {str(e)}")
            return json.dumps({
                "status": "error",
                "message": f"删除商机失败: {str(e)}"
            }, ensure_ascii=False)

    def sort_business(self, business_ids: str) -> str:
        """
        更新商机信息的排序顺序, 如果用户没有提供排序规则, 则按照下面规则排序

        # 通用排序提示词
        对商机数据进行智能排序，优先级从高到低：

        ## 排序规则
        1. 商机当前阶段接近成交的记录，优先级高于早期阶段。
        2. 备注或商机来源含“高潜力”“决策人”或来源为“推荐”等高可信渠道的记录，优先级略高。
        3. 创建日期较新的记录，优先级略高。

        ## 输出
        按优先级从高到低排序的记录列表，附带简要排序理由。

        Args:
            business_ids:  按优先级从高到低排好的商机ID列表(优先级高的在前面)，每个id之间用,间隔

        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"排序商机 ID: {business_ids}")
        try:
            if not business_ids:
                return json.dumps({
                    "status": "error",
                    "message": "business_ids不能为空"
                }, ensure_ascii=False)
            data: dict[str, Any] = {
                "ids": business_ids.replace(" ", "").split(","),  # 去除空格并分割,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/busiopp/sortAll", json=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"排序商机失败: {str(response.text)}")
                return json.dumps({"error": "排序商机失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"排序商机失败: {str(e)}")
            result = {
                "success": False,
                "message": f"排序商机失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    # 添加商机跟进记录
    def add_business_interact(self,
                              bo_id: int,
                              title: str,
                              content: str,
                              date: str,
                              is_finished: int
                              ) -> str:
        """添加商机跟进记录

            Args:
                bo_id: 商机ID
                title: 标题
                record_content: 内容
                date: 跟进记录的时间，字符串格式："Y-M-D H:M:S"
                is_finished: 跟进记录是否已完成： 1表示已完成，0表示未完成
        """
        try:
            data: dict[str, Any] = {
                "bo_id": bo_id,
                "title": title,
                "content": content,
                "date": date,
                "is_finished": is_finished,
                "user_id": get_context("user_id")
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/busiopp/addinteract", data=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"添加商机跟进记录失败: {str(response.text)}")
                return json.dumps({"error": "添加商机跟进记录失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"添加商机跟进记录失败: {str(e)}")
            sendErrorNotice(title="添加商机跟进记录失败", content=f"{str(e)}")
            result = {
                "success": False,
                "message": f"添加商机跟进记录失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

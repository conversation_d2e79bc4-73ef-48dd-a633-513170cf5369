from typing import List, Optional
from sqlalchemy import Column, Integer, String, DateTime, and_
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from orm.base_orm import FormattedDateTime
from utils.mysql import get_sync_mysql

Base = declarative_base()

class TeacherAgent(Base):
    """老师分身模型"""
    __tablename__ = 'teacher_agent'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), nullable=False, default='', comment='名称')
    app_id = Column(String(64), nullable=False, comment='应用id')
    created_at = Column(FormattedDateTime, default=datetime.now)
    updated_at = Column(FormattedDateTime, default=datetime.now, onupdate=datetime.now)
    deleted_at = Column(FormattedDateTime, nullable=True, default=None)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'app_id': self.app_id,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'deleted_at': self.deleted_at
        }

def create_teacher_agent(name: str, app_id: str) -> Optional[TeacherAgent]:
    """创建老师分身"""
    session = get_sync_mysql()
    with session() as session:
        try:
            teacher = TeacherAgent(name=name, app_id=app_id)
            session.add(teacher)
            session.commit()
            session.refresh(teacher)
            return teacher
        except Exception as e:
            session.rollback()
            raise e

def update_teacher_agent(teacher_id: int, name: str = None, app_id: str = None) -> Optional[TeacherAgent]:
    """更新老师分身"""
    session = get_sync_mysql()
    with session() as session:
        try:
            teacher = session.query(TeacherAgent).filter(
                and_(
                    TeacherAgent.id == teacher_id,
                    TeacherAgent.deleted_at.is_(None)
                )
            ).first()
            
            if not teacher:
                return None
                
            if name is not None:
                teacher.name = name
            if app_id is not None:
                teacher.app_id = app_id
            teacher.updated_at = datetime.now()
            
            session.commit()
            session.refresh(teacher)
            return teacher
        except Exception as e:
            session.rollback()
            raise e

def delete_teacher_agent(teacher_id: int) -> bool:
    """软删除老师分身"""
    session = get_sync_mysql()
    with session() as session:
        try:
            teacher = session.query(TeacherAgent).filter(
                and_(
                    TeacherAgent.id == teacher_id,
                    TeacherAgent.deleted_at.is_(None)
                )
            ).first()
            
            if not teacher:
                return False
                
            teacher.deleted_at = datetime.now()
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            raise e

def get_teacher_agent_by_id(teacher_id: int) -> Optional[TeacherAgent]:
    """根据ID获取老师分身"""
    session = get_sync_mysql()
    with session() as session:
        return session.query(TeacherAgent).filter(
            and_(
                TeacherAgent.id == teacher_id,
                TeacherAgent.deleted_at.is_(None)
            )
        ).first()

def get_all_teacher_agents() -> List[TeacherAgent]:
    """获取所有老师分身"""
    session = get_sync_mysql()
    with session() as session:
        return session.query(TeacherAgent).filter(
            TeacherAgent.deleted_at.is_(None)
        ).order_by(TeacherAgent.created_at.desc()).all()

def search_teacher_agents(keyword: str = None) -> List[TeacherAgent]:
    """搜索老师分身"""
    session = get_sync_mysql()
    with session() as session:
        query = session.query(TeacherAgent).filter(
            TeacherAgent.deleted_at.is_(None)
        )
        
        if keyword:
            query = query.filter(
                TeacherAgent.name.like(f'%{keyword}%')
            )
            
        return query.order_by(TeacherAgent.created_at.desc()).all()

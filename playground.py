from dotenv import load_dotenv
from agno.agent import Agent
from tools.Knowledge_tools import KnowledgeTools
from tools.attachment_tools import AttachmentTools
from utils.context import set_context
from utils.knowledge.user_file_storage import delete_user_file, write_user_file_by_url
from utils.storage import get_singlestore_storage, num_history_runs
from fastapi import Query, Depends
from copy import deepcopy
from agno.playground import serve_playground_app
from tools.extrac_img import extract_image_content
from utils.logger import logger
from textwrap import dedent
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from tools.todoist_tools import TodoistTools
from tools.customer_tools import CustomerTools
from tools.business_opportunities_tools import BusinessOpportunitiesTools
from tools.content_parse import convert_to_markdown
from tools.get_link import get_link
from utils.knowledge.vector import vector_add as util_vector_add, vector_del as util_vector_del, uri, tableName, searchType, embedder
from agno.agent import Agent
from utils.tool import get_attachment_url
from utils.tool_name_map import tool_name_map
from utils.claude_models import get_random_claude_model
from utils.models import mainModel, deepseek, glm4
from utils.redis import get_client
from tools.save_user_profile import save_user_profile, update_user_profile
from utils.doc_parse import read_document_from_url
from utils.models import openrouterClaude
from tools.baidu_search import web_search
from agno.playground import Playground, serve_playground_app
from utils.models import qwen_long
from agno.models.openai.like import OpenAILike
from agno.models.openai import OpenAIChat
from utils.context import get_context, set_context
from utils.agent_prompt import MAIN_INSTRUCTIONS
from tools.reasoning import ReasoningTools
# from agno.tools.reasoning import ReasoningTools
from tools.deepsearch_tools import deep_web_search_agent


load_dotenv()

memory = Memory(db=SqliteMemoryDb(
    table_name="team_memories", db_file="./memory.db"))


qwen = OpenAILike(
    id="qwen-plus-latest",
    api_key="sk-1900eb026aee4b95992186f796fdde08",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

gpt5mini = OpenAIChat(
    id="google/gemini-2.5-flash",
    api_key="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",
    base_url="https://openrouter.ai/api/v1",
    reasoning_effort="minimal"
)


mainAgent = Agent(
    name="AI助手",
    agent_id="001",
    model=gpt5mini,

    tools=[
        ReasoningTools(
            think=True,
            analyze=True,
            add_instructions=True,
        ),
        get_link,
        update_user_profile,
        deep_web_search_agent,
        TodoistTools(),
        CustomerTools(),
        BusinessOpportunitiesTools(),
        AttachmentTools(),
        KnowledgeTools(),
    ],
    telemetry=False,
    markdown=False,
    show_tool_calls=True,  # 显示工具调用
    stream_intermediate_steps=True,
    debug_mode=True,  # 启用调试模式
    add_datetime_to_instructions=True,

    add_context=True,
    add_history_to_messages=True,
    num_history_runs=num_history_runs,
    # memory=memory,
    # enable_agentic_memory=True,
    storage=get_singlestore_storage(
        table_name="main_agent_sessions"),
    instructions=MAIN_INSTRUCTIONS,

)

set_context("user_id", 4)

app = Playground(agents=[mainAgent]).get_app()

if __name__ == "__main__":
    serve_playground_app("playground:app", reload=True)

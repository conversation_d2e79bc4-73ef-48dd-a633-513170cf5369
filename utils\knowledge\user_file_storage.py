import os
import sys

from utils.doc_parse import read_document_from_url

root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(root_path)

import aiofiles
import json
from pathlib import Path
from typing import List, Dict, Optional
from utils.logger import logger


storage_pwd = os.path.join(root_path, 'tmp', 'storage', 'user_files')

async def write_user_file_by_url(user_id: str, file_id: str, read_url: str, file_type:str, file_name: str, view_url: str) -> bool:
    """
    通过读取url的内容异步写入用户文件
    Args:
        user_id: 用户ID
        file_id: 文件ID
        read_url: 要读取文件内容的url
        file_name: 文件名
        file_url: 文件URL
    Returns:
        是否成功
    """
    try:
        content = read_document_from_url(url=read_url, file_type=file_type)
        return await write_user_file(user_id=user_id, file_id=file_id, content=content, file_name=file_name, file_url=view_url)
    except Exception as e:
        logger.error(f'Error writing file {file_id}.md: {e}')
        return False

async def write_user_file(user_id: str, file_id: str, content: str, file_name: str, file_url: str) -> bool:
    """
    异步写入用户文件
    Args:
        user_id: 用户ID
        file_id: 文件ID
        content: 文件内容
        file_name: 文件名
        file_url: 文件URL
    Returns:
        是否成功
    """
    user_dir = Path(storage_pwd) / str(user_id)
    user_dir.mkdir(parents=True, exist_ok=True)

    file_data = f"# 文件名称\n{file_name}\n\n# 文件URL\n{file_url}\n\n# 文件内容\n{content}"

    file_path = user_dir / f'{file_id}.md'
    try:
        async with aiofiles.open(file_path, mode='w', encoding='utf-8') as f:
            await f.write(file_data)
        return True
    except Exception as e:
        logger.error(f'Error writing file {file_id}.md: {e}')
        return False

def delete_user_file(user_id: str, file_id: str) -> bool:
    """
    删除用户文件
    Args:
        user_id: 用户ID
        file_id: 文件ID
    Returns:
        是否成功
    """
    file_path = Path(storage_pwd) / str(user_id) / f'{file_id}.md'
    try:
        if file_path.exists():
            file_path.unlink()
        return True
    except Exception as e:
        logger.error(f'Error deleting file {file_id}.md: {e}')
        return False

async def read_user_files(user_id: str, ids: list = []) -> List[str]:
    """
    异步读取用户文档
    Args:
        user_id: 用户ID
        ids: 文档ID列表，如果不传则读取用户所有文档内容，如果传了则只读取指定ID的文档内容。如果传了ids，则返回的文档内容列表的顺序与ids列表的顺序一致。如果ids列表中有不存在的文档ID，则会跳过该文档ID，不会报错。
    Returns:
        List[str]: 文件内容列表
    """
    user_dir = Path(storage_pwd) / str(user_id)
    if not user_dir.exists():
        return []

    files = []
    file_list = [user_dir / f'{id}.md' for id in ids] if ids else user_dir.glob('*')
    for file_path in file_list:
        try:
            if not file_path.exists():
                continue
            async with aiofiles.open(file_path, mode='r', encoding='utf-8') as f:
                content = await f.read()
                files.append(content)
        except Exception as e:
            logger.error(f'Error reading file {file_path}: {e}')
    return files

if __name__ == "__main__":
    import asyncio
    loop = asyncio.new_event_loop()
    loop.run_until_complete(write_user_file(user_id=0, file_id=1, content="test1", file_name="test1.txt", file_url="test.cn/test1"))
    loop.run_until_complete(write_user_file(user_id=0, file_id=2, content="test2", file_name="test2.txt", file_url="test.cn/test2"))
    print(loop.run_until_complete(read_user_files(user_id=0)))
    delete_user_file(user_id=0, file_id=1)
    delete_user_file(user_id=0, file_id=2)

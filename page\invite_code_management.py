import streamlit as st
import random
import string
from utils.mysql import get_sync_mysql
from sqlalchemy import text
import pandas as pd

# 邀请码管理功能


def render_invite_code_management():
    st.header("邀请码管理")

    # 初始化状态变量
    if "invite_code_initialized" not in st.session_state:
        st.session_state.invite_code_initialized = True
        st.session_state.generation_success = False
        st.session_state.generation_error = None
        st.session_state.codes_generated = 0

    # 创建数据库连接
    try:
        conn = get_sync_mysql()
        db_connected = True
    except Exception as e:
        conn = None
        db_connected = False
        st.error(f"数据库连接失败: {e}")

    # 生成邀请码的函数
    def generate_invite_code(length=8):
        # 使用大写字母和数字
        chars = string.ascii_uppercase + string.digits
        code = ''.join(random.choice(chars) for _ in range(length))
        return code

    # 检查邀请码是否已存在
    def is_code_exists(code):
        with conn() as session:
            try:
                query = text(
                    "SELECT COUNT(*) FROM invite_code WHERE code = :code")
                result = session.execute(query, {"code": code}).scalar()
                return result > 0
            except Exception:
                return True  # 如果查询出错，默认认为代码已存在，避免重复

    # 保存邀请码到数据库
    def save_invite_code(code):
        with conn() as session:
            try:
                query = text(
                    "INSERT INTO invite_code (code, created_at, is_used) VALUES (:code, NOW(), 0)")
                session.execute(query, {"code": code})
                session.commit()
                return True
            except Exception as e:
                st.error(f"保存邀请码失败: {e}")
                session.rollback()
                return False

    # 获取所有邀请码
    def get_all_invite_codes():
        with conn() as session:
            try:
                query = text(
                    "SELECT code, created_at, is_used, user_id FROM invite_code ORDER BY id DESC")
                result = session.execute(query).fetchall()
                if result:
                    df = pd.DataFrame(
                        result, columns=["邀请码", "创建时间", "是否已使用", "激活用户ID"])
                    # 将使用状态从0/1转换为否/是
                    df["是否已使用"] = df["是否已使用"].apply(
                        lambda x: "是" if x == 1 else "")
                    df["激活用户ID"] = df["激活用户ID"].apply(
                        lambda x: x if x > 0 else "")
                    return df
                return pd.DataFrame(columns=["邀请码", "创建时间", "是否已使用", "激活用户ID"])
            except Exception as e:
                st.error(f"获取邀请码列表失败: {e}")
                return pd.DataFrame(columns=["邀请码", "创建时间", "是否已使用", "激活用户ID"])

    # 左侧放置生成功能，右侧显示已有邀请码
    col1, col2 = st.columns([1, 3])

    with col1:
        st.subheader("生成邀请码")

        # 表单用于生成新的邀请码
        with st.form(key="invite_code_form"):
            # 输入生成数量
            num_codes = st.number_input(
                "生成数量", min_value=1, max_value=100, value=5, step=1)

            # 提交按钮
            submit = st.form_submit_button(label="生成邀请码", type="primary")

            if submit and db_connected:
                with st.spinner("正在生成邀请码..."):
                    success_count = 0
                    for _ in range(num_codes):
                        # 生成唯一的邀请码
                        attempts = 0
                        max_attempts = 10  # 最多尝试10次
                        while attempts < max_attempts:
                            new_code = generate_invite_code()
                            if not is_code_exists(new_code):
                                if save_invite_code(new_code):
                                    success_count += 1
                                break
                            attempts += 1

                    if success_count > 0:
                        st.session_state.generation_success = True
                        st.session_state.codes_generated = success_count
                    else:
                        st.session_state.generation_error = "无法生成唯一的邀请码，请稍后再试"

    # 显示生成结果
    if st.session_state.get('generation_success', False):
        st.success(f"成功生成 {st.session_state.codes_generated} 个邀请码！")
        st.session_state.generation_success = False
        st.session_state.codes_generated = 0

    if st.session_state.get('generation_error'):
        st.error(st.session_state.generation_error)
        st.session_state.generation_error = None

    with col2:
        st.subheader("邀请码列表")
        if db_connected:
            # 获取并显示所有邀请码
            codes_df = get_all_invite_codes()
            st.dataframe(codes_df, use_container_width=True)

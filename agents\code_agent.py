from agno.agent import Agent
from agno.models.openai import OpenAIChat
from utils.storage import get_singlestore_storage

# 获取SingleStore存储实例
storage = get_singlestore_storage(table_name="code_sessions")

# 创建代码生成智能体
code_agent = Agent(
    name="代码生成助手",
    model=OpenAIChat(id="gpt-4o"),
    storage=storage,
    markdown=True,
    instructions="""
    你是一个专业的代码生成助手。你可以帮助用户:
    - 根据需求生成完整的代码实现
    - 解释复杂的代码逻辑
    - 优化现有代码
    - 提供代码示例和最佳实践
    - 帮助解决编程问题和调试
    
    请生成清晰、注释完善、结构良好的代码。
    """
) 
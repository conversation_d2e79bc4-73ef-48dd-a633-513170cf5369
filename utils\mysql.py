from typing import Callable
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy import create_engine
import os
from dotenv import load_dotenv

load_dotenv()

async_session = None
sync_session = None

USERNAME = os.getenv('MYSQL_USERNAME')
PASSWORD = os.getenv('MYSQL_PASSWORD')
HOST = os.getenv('MYSQL_HOST')
PORT = os.getenv('MYSQL_PORT')
DATABASE = os.getenv('MYSQL_DATABASE')

def get_async_mysql() -> Callable[..., AsyncSession]:
    """
    创建并返回一个异步mysql工厂实例
    
    返回:
        AsyncSession工厂实例
    """
    global async_session
    if async_session:
        return async_session

    # 异步引擎URL
    db_url = f"mysql+aiomysql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}?charset=utf8mb4"
    
    # 创建异步引擎
    engine = create_async_engine(
        db_url,
        echo=True,  # 设置为True可以查看SQL日志
        pool_size=20,  # 连接池大小
        max_overflow=10,  # 最大溢出连接数
        pool_pre_ping=True  # 自动检测连接是否有效
    )
    
    # 创建异步会话工厂
    async_session = sessionmaker(
        engine, expire_on_commit=False, class_=AsyncSession
    )
    
    return async_session

def get_sync_mysql():
    """
    创建并返回一个同步mysql会话工厂
    
    返回:
        scoped_session工厂实例
    """
    global sync_session
    if sync_session:
        return sync_session

    # 同步引擎URL
    db_url = f"mysql+pymysql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}?charset=utf8mb4"
    
    # 创建同步引擎
    engine = create_engine(
        db_url,
        echo=True,  # 设置为True可以查看SQL日志
        pool_size=20,  # 连接池大小
        max_overflow=10,  # 最大溢出连接数
        pool_pre_ping=True  # 自动检测连接是否有效
    )
    
    # 创建同步会话工厂
    sync_session = scoped_session(
        sessionmaker(
            bind=engine,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )
    )
    
    return sync_session
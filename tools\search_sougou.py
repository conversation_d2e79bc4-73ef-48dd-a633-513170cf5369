import traceback
import requests
import os
import sys
from typing import Dict, Any
from utils.logger import logger

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 将项目根目录添加到Python路径
if project_root not in sys.path:
    sys.path.append(project_root)


def web_search(query: str, count: int = 10, search_recency_filter: str = "noLimit", search_domain_filter: str = "") -> Dict[Any, Any]:
    """用于网络搜索的工具

    Args:
        query: 要搜索的内容
        count: 返回结果的数量，默认为10，最大为50
        search_recency_filter: 搜索结果的时间限制，可选值为"noLimit", "oneDay", "oneWeek", "oneMonth", "oneYear"，默认为"noLimit"
        search_domain_filter: 搜索结果的域名限制(只能传一个域名)，默认为不限制
    """

    token = os.getenv("BAIDU_SEARCH_KEY")

    # API请求URL和头部
    url = "https://open.bigmodel.cn/api/paas/v4/web_search"
    headers = {
        "Authorization": "96edd18843204576adf21836977b5e67.T2pNHKJYF06iXXdK",
        "Content-Type": "application/json"
    }

    # 构建请求体
    payload = {
        "search_query": query,
        "search_engine": "search_pro_sogou",
        "search_intent": "false",
        "count": count,
        # "content_size": content_size, # 返回内容大小，可选值为"medium"(摘要信息), "high"(详细信息)，默认为"medium"
        "search_recency_filter": search_recency_filter,
        "search_domain_filter": search_domain_filter
    }

    try:

        # 发送POST请求
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()  # 如果请求失败，抛出异常
        result = response.json()  # 返回JSON响应
        references = result.get("search_result", [])
        extracted_results = []
        for reference in references:
            content = reference.get("content", "")
            date = reference.get("publish_date", "")
            title = reference.get("title", "")
            url = reference.get("link", "")
            extracted_results.append({
                "content": content,
                "date": date,
                "title": title,
                "url": url
            })

        return extracted_results

    except requests.exceptions.RequestException as e:
        logger.error(f"网络搜索请求失败：{str(e)}")
        return {"error": str(e), "status": "failed"}
    except Exception as e:
        logger.error(f"网络搜索异常：{str(e)}")
        return {"error": str(e), "status": "failed"}


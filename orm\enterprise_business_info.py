from typing import List
from sqlalchemy import Column, Integer, String, Text, DateTime, or_
from sqlalchemy.ext.declarative import declarative_base
import zlib
import json
from datetime import datetime
from orm.base_orm import FormattedDateTime
from utils.mysql import get_sync_mysql

Base = declarative_base()

class EnterpriseBusinessInfo(Base):
    """企业工商信息模型"""
    __tablename__ = 'enterprise_business_info'

    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, index=True, comment="公司ID")
    name = Column(String(255), index=True, comment="公司名称")
    reg_number = Column(String(50), index=True, comment="注册号")
    credit_code = Column(String(255), index=True, comment="统一社会信用代码")
    info = Column(Text, comment="详细信息压缩json")  # 存储压缩后的JSON数据
    source = Column(Integer, comment="数据来源：1-天眼查")
    created_at = Column(FormattedDateTime, default=datetime.now)
    updated_at = Column(FormattedDateTime, default=datetime.now, onupdate=datetime.now)
    deleted_at = Column(FormattedDateTime, nullable=True, default=None)

def add_one(company_id, name: str, reg_number: str, credit_code: str, info, source):
    """添加一条企业工商信息记录"""
    # 已存在则更新
    existing_record = query_one(company_id=company_id, name=name, reg_number=reg_number, credit_code=credit_code, fields=["id"])
    info_str = json.dumps(info, ensure_ascii=False) if not isinstance(info, str) else info
    
    session = get_sync_mysql()()
    try:
        new_record = EnterpriseBusinessInfo(
            company_id=company_id,
            name=name,
            reg_number=reg_number,
            credit_code=credit_code,
            info=zlib.compress(info_str.encode('utf-8')),
            source=source
        )
        if existing_record:
            # 更新已存在的记录
            new_record.id = existing_record["id"]
            session.merge(new_record)
        else:
            # 添加新记录
            session.add(new_record)
        session.commit()
        return new_record.id
    except Exception as e:
        session.rollback()
        raise
    finally:
        session.close()

def query_one(company_id="", name="", reg_number="", credit_code="", fields: List[str] = ["id", "company_id", "name", "reg_number", "credit_code", "info", "source", "created_at", "updated_at"]):
    """查询一条企业工商信息记录, or条件查询"""
    session = get_sync_mysql()
    with session() as session:
        query = session.query(EnterpriseBusinessInfo)
        
        conditions = []
        if company_id:
            conditions.append(EnterpriseBusinessInfo.company_id == str(company_id).strip())
        if name:
            conditions.append(EnterpriseBusinessInfo.name == name.strip())
        if reg_number:
            conditions.append(EnterpriseBusinessInfo.reg_number == reg_number.strip())
        if credit_code:
            conditions.append(EnterpriseBusinessInfo.credit_code == credit_code.strip())
            
        if conditions:
            query = query.filter(or_(*conditions))
        else:
            return None
        query = query.with_entities(*[getattr(EnterpriseBusinessInfo, field) for field in fields])
            
        result = query.first()
        if result:
            dict_result = {field: getattr(result, field) for field in fields}
            if "info" in dict_result: # 解压缩info字段
                decompressed_info = zlib.decompress(dict_result["info"]).decode('utf-8')
                dict_result["info"] = json.loads(decompressed_info) if decompressed_info else None
            return dict_result
        return None

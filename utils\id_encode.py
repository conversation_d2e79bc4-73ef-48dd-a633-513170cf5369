class IdEncode:
    # 密钥
    key = 'reKLsHRkwBIciaj57MxzFGymVNtYQ2EJ8g4PDUpO1d69noCXqfZvW0SAul3hTb'
    keyLen = 62

    @staticmethod
    def encode(int_val):
        # 判断是否为整型
        if not isinstance(int_val, int):
            try:
                int_val = int(int_val)
            except ValueError:
                return ''
        int_val = 100000 + int_val  # 保证输出的加密字符长度至少是7位
        # 将传入数字转换成十六进制分割成数组
        hex_str = hex(int_val)[2:]  # 去掉 '0x' 前缀
        hex_arr = list(hex_str)
        # 将密钥分割成数组
        key_arr = list(IdEncode.key)
        # 随机数字
        import random
        rand = random.randint(0, IdEncode.keyLen - 1)
        # 将随机值压入结果开头
        result_str = key_arr[rand]
        # 验证码
        verfy = key_arr[(IdEncode.keyLen - rand + len(str(int_val))) % IdEncode.keyLen]
        # 循环十六进制每一位数字，替换成密钥里的值
        for v in hex_arr:
            offset = int(v, 16) + rand
            result_str += key_arr[offset % IdEncode.keyLen]
        # 将验证码压入结果末尾并返回
        return result_str + verfy

    @staticmethod
    def decode(encoded_str):
        import re
        if not re.match(r'^[0-9a-zA-Z]{2,10}$', encoded_str):
            return ''
        # 将传入字符串分割成数组
        str_arr = list(encoded_str)
        # 密钥
        key = IdEncode.key
        # 将密钥分割成数组
        key_arr = list(IdEncode.key)
        # 密钥长度
        keyLen = IdEncode.keyLen
        # 十六进制数值
        hex_val = ''
        # 获取随机数
        rand = key.index(str_arr.pop(0))
        # 获取验证码
        verfy = str_arr.pop()
        # 循环每一个字串并转换成十六进制
        for v in str_arr:
            if key.index(v) >= rand:
                hex_val += hex(key.index(v) - rand)[2:]
            else:
                hex_val += hex(keyLen - rand + key.index(v))[2:]
        # 十六进制转换成十进制
        dec = int(hex_val, 16)
        # 判断验证码是否正确
        if verfy != key_arr[(keyLen - rand + len(str(dec))) % keyLen]:
            return ''
        dec = dec - 100000
        return dec

if __name__ == '__main__':
    encode = IdEncode.encode(30)
    print(encode, IdEncode.decode(encode))
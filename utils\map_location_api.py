
import os
from dotenv import load_dotenv
import requests

from utils.logger import logger

load_dotenv()

tx_key = os.getenv("TX_MAP_KEY")
tx_host = "https://apis.map.qq.com"

def tx_place_search(query: str, filter: str, page_num: int = 1, page_size: int = 20):
    """
    地点搜索，可以通过地址、名称、分类等关键词搜索获取相关的地点名称、分类、经纬度、地址等信息
    """
    url = tx_host + f"/ws/place/v1/search"
    data = {
        "keyword": query,
        "boundary": "nearby(40.040589,116.273543,1000)", # 经纬度和半径(暂时没用上,固定写死一个)
        "filter": filter,
        "page_index": page_num,
        "page_size": page_size,
        "key": tx_key,
    }
    return requests.get(url, params=data)
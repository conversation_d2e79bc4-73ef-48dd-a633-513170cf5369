from agno.models.openai import OpenAIChat
from agno.models.openai.like import OpenAILike
from agno.models.openrouter import OpenRouter
from openai import OpenAI

mainModel = OpenAIChat(
    id="gpt-4o",
    base_url="http://ai.profly.com.cn:8499/v1",
    api_key="***************************************************"
)

gpt41 = OpenAIChat(
    id="gpt-4.1",
    base_url="http://ai.profly.com.cn:8499/v1",
    api_key="***************************************************"
)

# mainModel = OpenAILike(
#     id="qwen3-235b-a22b",
#     api_key="sk-1900eb026aee4b95992186f796fdde08",
#     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
# )

qwen_key = "sk-1900eb026aee4b95992186f796fdde08"
qwen_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
qwen_long = OpenAILike(
    id="qwen-long",
    api_key=qwen_key,
    base_url=qwen_url,
)

qwen_plus = OpenAILike(
    id="qwen-plus-latest",
    api_key=qwen_key,
    base_url=qwen_url,
)

qwen_turbo = OpenAILike(
    id="qwen-turbo",
    api_key=qwen_key,
    base_url=qwen_url,
)

glm4 = OpenAILike(
    id="glm-4-plus",
    api_key="96edd18843204576adf21836977b5e67.T2pNHKJYF06iXXdK",
    base_url="https://open.bigmodel.cn/api/paas/v4/"
)

deepseek = OpenAILike(
    id="deepseek-chat",
    api_key="***********************************",
    base_url="https://api.deepseek.com",
)

deepseek_r1 = OpenAILike(
    id="deepseek-reasoner",
    api_key="***********************************",
    base_url="https://api.deepseek.com",
)

deepseek_client = OpenAI(
    api_key="***********************************", base_url="https://api.deepseek.com")

openrouterClaude = OpenRouter(
    id="anthropic/claude-3.5-haiku-20241022",
    api_key="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",
    max_tokens=None,
)

gemini25 = OpenRouter(
    id="google/gemini-2.5-pro-preview-05-06",
    api_key="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",
    max_tokens=None,
    base_url="http://ai.profly.com.cn:8599/api/v1"
)

openrouterGpt = OpenRouter(
    id="openai/gpt-4.1",
    api_key="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",
    max_tokens=None,
    # base_url="http://ai.profly.com.cn:8599/api/v1"
)

gpt5mini = OpenAIChat(
    id="openai/gpt-5-mini",
    api_key="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",
    base_url="https://openrouter.ai/api/v1",
    reasoning_effort="minimal"
)

KimiK2 = OpenAILike(
    id="kimi-k2-0711-preview",
    api_key="sk-MIHBcgSuve4AaRA5NFWzGCWgvFlOb0snix1jZEHLJh7YbdWe",
    base_url="https://api.moonshot.cn/v1",
)

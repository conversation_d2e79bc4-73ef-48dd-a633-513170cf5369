import traceback
from agno.tools import Toolkit
import pymysql
from typing import List, Dict, Optional, Any
from datetime import datetime
import json
import logging
import requests
from utils.context import get_context
from utils.knowledge.user_file_storage import read_user_files
from utils.logger import logger
from utils.models import qwen_long
from agno.models.message import Message


class KnowledgeTools(Toolkit):
    """用于知识库搜索的工具"""

    def __init__(self):
        super().__init__(name="Knowledge_tools")

        # 注册工具函数
        self.register(self.knowledge_llm_search)
        logger.info("KnowledgeTools工具注册完成")

    async def knowledge_llm_search(self, query: str, file_ids: list[int] = []) -> str:
        """
        查询用户的产品文档，产品介绍，公司介绍等相关内容
        Args:
            query (str): 查询内容
            file_ids (list, optional): 文件ID列表，如果不传则搜索所有文件，如果传了则只搜索指定ID的文件
        Returns:
            str: RAG检索结果
        """
        try:
            user_id = get_context("user_id")
            # 优化点1: 合并知识库工具, 先手动查询知识库, 如果没有匹配的结果再调用大模型查询
            # 优化点2: 保存文档时增加文档总结, 先从所有文档总结找到匹配的文档, 再对文档进行问答

            # 调用大模型
            # prompt = f"# 任务目标\n你是一个RAG工具，任务是根据下面的问题从给定的知识库内容中找到最相关的{top_k}段内容返回原文，不要做任何总结和说明，不要丢失任何细节，并需要注明每段内容所属的文件名和url，每段的文本不超过500字\n\n# 问题：{query}\n\n# 知识库内容\n"
            prompt = f"# 任务目标\n你是一个RAG问答助手，任务是根据下面的问题从给定的知识库内容中找到最相关的内容进行解答，并需要注明所有引用到的内容所属的文件名和url\n\n# 问题：{query}\n\n# 知识库内容\n"
            response = qwen_long.aresponse_stream(
                messages=[Message(role="user", content=prompt + str(await read_user_files(user_id=user_id, ids=file_ids)))]
            )
            content = ""
            async for chunk in response:
                content += chunk.content if chunk.content else ""

            logger.info(f"大模型搜索知识库成功: {content}")

            return json.dumps({
                "results": content
            }, ensure_ascii=False)

        except Exception as e:
            logger.error(f"大模型搜索知识库失败: {traceback.format_exc()}")
            return str({"status": "error", "message": f"大模型搜索知识库失败: {e}"})

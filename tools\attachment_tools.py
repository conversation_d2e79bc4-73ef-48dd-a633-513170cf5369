import asyncio
import os
from pathlib import Path
import re
import tempfile
import traceback
from agno.tools import Toolkit
from httpx import stream
from markitdown import MarkItDown
import pymysql
from typing import List, Dict, Optional, Any
from datetime import datetime
import json
import logging
import pypandoc
import requests
from sqlalchemy import text
from utils.ali_docmind import <PERSON><PERSON><PERSON><PERSON>
from utils.context import get_context
from utils import pyapi
from utils.excel import ExcelConverter
from utils.logger import logger
from utils.mysql import get_sync_mysql
from utils.oss import upload_file as oss_upload_file
from utils.tool import get_attachment_url
from utils.word import WordUtil
from pdf2docx import Converter
from utils.queue import task_queue

def generate_excel(tables: list[dict], name: str, add_title_row: bool = True) -> str:
    """
    生成Excel文档

    Args:
        tables (list[dict]): 表格内容, 每个元素是一个字典，包含下面的字段:
            title (str): 表格标题
            header (list): 表头
            data (list): 表格数据, 每个元素是一个列表，表示一行数据
        name (str): 文档名称
        add_title_row (bool, optional): 是否为每个工作表添加标题行，默认为True
    """
    try:
        content_type = "xlsx"
        if f".{content_type}" not in name:
            name = f"{name}.{content_type}"
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=f".{content_type}", delete=False) as tmp_file:
            tmp_file_path = tmp_file.name
        result = {"status": "success", "message": "文档生成成功",
                    "file_name": name, "file_type": content_type}
        converter = ExcelConverter(add_title_row=add_title_row)
        # table格式校验
        for table in tables:
            if "title" not in table:
                raise ValueError(f"表格缺少title字段")
            if "header" not in table:
                raise ValueError(f"表格缺少header字段")
            if "data" not in table:
                raise ValueError(f"表格缺少data字段")
            if not isinstance(table["header"], list):
                raise ValueError(f"表格header字段必须是列表")
            if not isinstance(table["data"], list):
                raise ValueError(f"表格data字段必须是列表")
            for row in table["data"]:
                if not isinstance(row, list):
                    raise ValueError(f"表格data字段中的每个元素必须是列表")
            
        converter.fill_tables(tables).save(tmp_file_path)

        return upload_file(tmp_file_path, name, content_type)
    except Exception as e:
        if not isinstance(e, ValueError):
            logger.error(f"文档生成失败: {str(e)}, traceback: {traceback.format_exc()}")
        result = {"status": "error", "message": f"文档生成失败: {str(e)}"}
    finally:
        if os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)
    return str(result)

def upload_file(file_path: str, name: str, content_type: str) -> str:
    """
    上传文件

    Args:
        file_path (str): 本地文件路径
        name (str): 文件名称
        content_type (str, optional): 文件类型(后缀名)
    """
    try:
        if not os.path.exists(file_path):
            raise ValueError(f"文件不存在: {file_path}")
        user_id = get_context("user_id")
        if f".{content_type}" not in name:
            name = f"{name}.{content_type}"
        # 上传文件到OSS
        object = oss_upload_file(user_id, file_path, content_type, name)
        if not object:
            raise ValueError(f"文件上传失败")
        
        # 插入附件表并获取id
        filesize = os.path.getsize(file_path)
        sync_session = get_sync_mysql()
        with sync_session() as session:
            exec_result = session.execute(
                text("INSERT INTO `attachment` (`user_id`, `name`, `type`, `url`, `size`, `file_hash`, `classify`) VALUES (:user_id, :name, :type, :url, :size, :file_hash, 0)"),
                {"user_id": user_id, "name": name, "type": content_type, "url": object, "size": filesize, "file_hash": object.split("/")[-1].split(".")[0]}
            )
            attachment_id = exec_result.lastrowid
            session.commit()
            result = {"status": "success", "message": "文件上传成功", "file_name": name, "file_type": content_type, "file_size": filesize, "file_url": get_attachment_url(attachment_id, content_type)}
        # 调用文件归档(队列异步处理)
        task_queue.add_task(
            pyapi.async_call_rpc, "\\app\\api\\service\\AttachmentService", "autoClassifyByAttachmentIds", {
                "userId": user_id, "attachmentIds": [attachment_id]
            })
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}, traceback: {traceback.format_exc()}")
        result = {"status": "error", "message": f"文件上传失败: {str(e)}"}
    # finally:
    #     if os.path.exists(file_path):
    #         os.unlink(file_path)
    return str(result)

class AttachmentTools(Toolkit):
    """用于管理附件信息的工具"""

    def __init__(self, **kwargs):
        super().__init__(name="attachment_tools", tools=[
            self.list_attachments,
            self.save_attachments,
            generate_document,
            generate_excel,
            self.create_directory,
            self.move_attachment,
            self.rename_attachment,
            self.delete_attachment,
            self.pdf_to_docx
        ], **kwargs)
        logger.info("AttachmentTools工具注册完成")

    def list_attachments(self, classify: str = "", classify_id: str = "", type: str = "", name: str = "") -> str:
        """
        获取文件列表，给用户发的每个文件都要附上文件url，所有参数为非必填，仅在需要的时候传入，不传为查全部
        Args:
            classify (str, optional): 分类, 1 客户文件，2 商机文件，3 销售物料，4 客户资料（包含客户文件和商机文件），5 其他
            classify_id (str, optional): 分类ID，classify=1时为客户id，classify=2时为商机id
            type (str, optional): 类型，如docx、xlsx、pdf等，其中dir表示文件夹、file表示所有类型的文件
            name (str, optional): 名称模糊搜索，模糊搜索没有找到的话去掉模糊搜索再找一遍
        """
        if name:
            name = f"%{name}%"
        return pyapi.call_api(f"/pyapi/attachment/list?limit=9999&classify={classify}&classify_id={classify_id}&type={type}&name={name}", method="GET")

    def save_attachments(self) -> str:
        """
        保存最近发送的文件
        """
        return pyapi.call_rpc("\\app\\api\\service\\AttachmentService", "autoClassifyByAttachmentIds", {
            "userId": get_context("user_id")
        })
    
    def create_directory(self, name: str, parent_id: int = 0) -> str:
        """
        在销售物料下创建目录

        Args:
            name (str): 目录名
            parent_id (int, optional): 父级目录ID，根目录为0
        """
        return pyapi.call_api("/pyapi/attachment/save", {
            "name": name, "parent_id": parent_id, "type": "dir", "classify": 3
        })

    def move_attachment(self, id: int, classify: int, classify_id: int = 0, parent_id: int = 0) -> str:
        """
        移动文件或目录，移动后的文件classify和classify_id要和父级目录保持一致，parent_id需为父级目录id

        Args:
            id (int): 文件或目录ID，可通过list_attachments获取
            classify (int): 移动后的附件分类, 1 客户文件，2 商机文件，3 销售物料，5 其他
            classify_id (int, optional): 移动后的分类ID，所属客户(classify=1)或商机(classify=2)id，classify等于1和2的时候必须
            parent_id (int, optional): 要移动到的父级目录ID，根目录为0
        """
        return pyapi.call_api("/pyapi/attachment/save", {
            "id": id, "classify": classify, "classify_id": classify_id, "parent_id": parent_id
        })

    def rename_attachment(self, id: int, name: str) -> str:
        """
        重命名文件或目录

        Args:
            id (int): 文件或目录ID，可通过list_attachments获取
            name (str): 新文件或目录名
        """
        return pyapi.call_api("/pyapi/attachment/save", {
            "id": id, "name": name
        })

    def delete_attachment(self, ids: list[int]) -> str:
        """
        批量删除文件或目录, 删除目录后目录下的文件将会不可见, 如果要同时删除目录下的文件需要将文件和目录的id都传进来, 如果只删除目录不需要删目录下的文件需要先将目录下的文件移动到其他目录下

        Args:
            ids (list[int]): 文件或目录ID列表，可通过list_attachments获取
        """
        return pyapi.call_api("/pyapi/attachment/del", {
            "id": ids
        })

    def pdf_to_docx(self, file_url: str, file_name: str) -> str:
        """
        pdf转word文档(docx)

        Args:
            file_url (str): 文件URL
            file_name (str): 文件名
        """
        try:
            file_name = file_name.replace(".pdf", ".docx").replace(".PDF", ".docx")
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
                pdf_tmp_file_path = tmp_file.name
            with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as tmp_file:
                docx_tmp_file_path = tmp_file.name
            
            # 下载文件
            response = requests.get(file_url, timeout=60, headers={"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"})
            response.raise_for_status()
            with open(pdf_tmp_file_path, "wb") as f:
                f.write(response.content)
            # 转换文件
            # 如果文件内容为空可能是扫描件解析不了, 改用支持扫描件文档解析后的markdown来生成docx
            md = MarkItDown(enable_plugins=False) # Set to True to enable plugins
            result = md.convert(pdf_tmp_file_path)
            if result.text_content.strip() == "":
                docmind = AliDocmind()
                content, _ = docmind.parse_file(file_url, "pdf")
                return self.generate_document(content, file_name, "docx")
            cv = Converter(pdf_tmp_file_path)
            cv.convert(docx_tmp_file_path)
            cv.close()
            WordUtil(docx_tmp_file_path).set_global_font("宋体", "宋体").save(docx_tmp_file_path)
            # 上传文件
            return upload_file(docx_tmp_file_path, file_name, "docx")
        except Exception as e:
            logger.error(f"pdf转docx失败: {str(e)}, traceback: {traceback.format_exc()}")
            return str({"status": "error", "message": f"pdf转docx失败: {str(e)}"})
        finally:
            if os.path.exists(pdf_tmp_file_path):
                os.unlink(pdf_tmp_file_path)
            if os.path.exists(docx_tmp_file_path):
                os.unlink(docx_tmp_file_path)

def generate_document(content: str, name: str, content_type: str = "docx") -> str:
    """
    生成文档

    Args:
        content (str): 文档内容（markdown格式）
        name (str): 文档名称
        content_type (str, optional): 文档类型，支持docx。
    """
    root_path = str(Path(__file__).parent.parent) # 获取项目根目录路径
    try:
        if f".{content_type}" not in name:
            name = f"{name}.{content_type}"
        # 创建临时文件并将markdown转为成指定的文档类型写入
        with tempfile.NamedTemporaryFile(suffix=f".{content_type}", delete=False) as tmp_file:
            tmp_file_path = tmp_file.name
        result = {"status": "success", "message": "文档生成成功",
                    "file_name": name, "file_type": content_type}
        if content_type in ["docx"]:
            # md横线会导致内容排版变成竖向
            pypandoc.convert_text(re.sub(r'^\s*---\s*$', '', content, flags=re.MULTILINE),
                                    content_type, format='md', outputfile=tmp_file_path)
            WordUtil(tmp_file_path).set_global_font("宋体", "宋体").save(tmp_file_path)
        else:
            raise ValueError(f"不支持的文档类型{content_type}")
        return upload_file(tmp_file_path, name, content_type)
    except Exception as e:
        if not isinstance(e, ValueError):
            logger.error(f"文档生成失败: {str(e)}, traceback: {traceback.format_exc()}")
        result = {"status": "error", "message": f"文档生成失败: {str(e)}"}
    finally:
        if os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)
    return str(result)
    

import os
from dotenv import load_dotenv
import requests

from orm import enterprise_business_info
from utils.logger import logger

load_dotenv()

tianyancha_token = os.getenv("TIANYANCHA_TOKEN")
tianyancha_host = "http://open.api.tianyancha.com"
tianyancha_headers = {
    "Authorization": tianyancha_token,
}

def tyc_bids_search(keyword: str, page_num: int = 1, type: int = None, province: str = "", searchType: int = None, publishStartTime: str = "", publishEndTime: str = ""):
    """
    通过招投标标题、采购人、公告类型等方式，查询招投标的有关信息
    Args:
        keyword(str): 搜索关键词（标题，采购人，供应商）
        page_num(int): 页码
        type(int): 公告类型 1. 招标预告、2. 招标公告、3. 招标变更（废弃）、4. 中标结果
        province(str): 省份地区
        searchType(int): 查询类别 1-标题 2-采购人 3-供应商
        publishStartTime(str): 发布开始时间
        publishEndTime(str): 发布结束时间
    """
    url = tianyancha_host + f"/services/open/m/bids/search"
    data = {
        "keyword": keyword,
        "pageNum": page_num,
        "pageSize": 20,
    }
    if type:
        data["type"] = type
    if province:
        data["province"] = province
    if searchType:
        data["searchType"] = searchType
    if publishStartTime:
        data["publishStartTime"] = publishStartTime
    if publishEndTime:
        data["publishEndTime"] = publishEndTime
    return requests.get(url, headers=tianyancha_headers, params=data)

def tyc_enterprise_search(query: str, page_size: int = 20, page_num: int = 1):
    """
    企业搜索，可以通过关键词获取企业列表，企业列表包括公司名称或ID、类型、成立日期、经营状态、统一社会信用代码等字段的详细信息
    """
    url = tianyancha_host + f"/services/open/search/2.0"
    data = {
        "word": query,
        "pageNum": page_num,
        "pageSize": page_size,
    }
    return requests.get(url, headers=tianyancha_headers, params=data)

def tyc_business_info(query: str):
    """
    企业工商信息查询，可以通过公司名称或ID获取包含企业基本信息、主要人员、股东信息、对外投资、分支机构等维度的相关信息
    """
    url = tianyancha_host + f"/services/open/cb/ic/2.0"
    data = {
        "keyword": query,
    }
    result = enterprise_business_info.query_one(company_id=query, name=query, reg_number=query, credit_code=query)
    if result:
        logger.info(f"企业工商信息数据库查询命中, query={query}")
        return {"result": result["info"]}
    logger.info(f"企业工商信息数据库查询未命中, 请求接口获取, query={query}")
    rsp = requests.get(url, headers=tianyancha_headers, params=data)
    data = rsp.json()
    result = data.get("result", {})
    if result:
        enterprise_business_info.add_one(company_id=result["id"], name=result["name"], reg_number=result["regNumber"], credit_code=result["creditCode"], info=result, source=1)
    return data
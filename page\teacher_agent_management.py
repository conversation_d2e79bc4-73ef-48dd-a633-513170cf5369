import streamlit as st
import pandas as pd
from datetime import datetime
from orm.teacher_agent import (
    create_teacher_agent, update_teacher_agent, delete_teacher_agent,
    get_all_teacher_agents, search_teacher_agents, get_teacher_agent_by_id
)
from orm.user_teacher import (
    create_user_teacher, update_user_teacher, delete_user_teacher,
    get_user_teachers_by_teacher_id, search_user_teachers
)

def render_teacher_agent_management():
    """渲染老师分身管理页面"""
    st.header("老师分身管理")
    
    # 初始化会话状态
    if "selected_teacher_id" not in st.session_state:
        st.session_state.selected_teacher_id = None
    if "show_add_teacher_form" not in st.session_state:
        st.session_state.show_add_teacher_form = False
    if "show_edit_teacher_form" not in st.session_state:
        st.session_state.show_edit_teacher_form = False
    if "show_add_invite_code_form" not in st.session_state:
        st.session_state.show_add_invite_code_form = False
    
    # 创建两列布局
    left_col, right_col = st.columns([1, 2])
    
    # 左侧：老师分身管理
    with left_col:
        st.subheader("老师分身列表")
        
        # 搜索框
        search_keyword = st.text_input("搜索老师名称", key="teacher_search")
        
        # 添加老师按钮
        if st.button("➕ 添加老师分身", key="add_teacher_btn"):
            st.session_state.show_add_teacher_form = True
            st.session_state.show_edit_teacher_form = False
        
        # 添加老师表单
        if st.session_state.show_add_teacher_form:
            with st.form("add_teacher_form"):
                st.write("**添加新老师分身**")
                teacher_name = st.text_input("老师名称", key="new_teacher_name")
                app_id = st.text_input("应用ID", key="new_app_id")
                
                col1, col2 = st.columns(2)
                with col1:
                    if st.form_submit_button("保存", type="primary"):
                        if teacher_name and app_id:
                            try:
                                create_teacher_agent(teacher_name, app_id)
                                st.success("老师分身添加成功！")
                                st.session_state.show_add_teacher_form = False
                                st.rerun()
                            except Exception as e:
                                st.error(f"添加失败：{e}")
                        else:
                            st.error("请填写完整信息")
                
                with col2:
                    if st.form_submit_button("取消"):
                        st.session_state.show_add_teacher_form = False
                        st.rerun()
        
        # 获取老师列表
        try:
            if search_keyword:
                teachers = search_teacher_agents(search_keyword)
            else:
                teachers = get_all_teacher_agents()
        except Exception as e:
            st.error(f"获取老师列表失败：{e}")
            teachers = []
        
        # 显示老师列表
        if teachers:
            for teacher in teachers:
                # 创建老师卡片
                is_selected = st.session_state.selected_teacher_id == teacher.id
                
                # 使用容器来创建可点击的卡片效果
                with st.container():
                    if is_selected:
                        st.markdown("""
                        <div style="background-color: #e6f3ff; padding: 10px; border-radius: 5px; border-left: 4px solid #0066cc; margin-bottom: 10px;">
                        """, unsafe_allow_html=True)
                    else:
                        st.markdown("""
                        <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; cursor: pointer;">
                        """, unsafe_allow_html=True)
                    
                    col1, col2, col3 = st.columns([3, 1, 1])
                    
                    with col1:
                        if st.button(f"**{teacher.name}**\n`{teacher.app_id}`", 
                                   key=f"select_teacher_{teacher.id}",
                                   help="点击选择此老师"):
                            st.session_state.selected_teacher_id = teacher.id
                            st.session_state.show_edit_teacher_form = False
                            st.rerun()
                    
                    with col2:
                        if st.button("✏️", key=f"edit_teacher_{teacher.id}", help="编辑"):
                            st.session_state.selected_teacher_id = teacher.id
                            st.session_state.show_edit_teacher_form = True
                            st.session_state.show_add_teacher_form = False
                            st.rerun()
                    
                    with col3:
                        if st.button("🗑️", key=f"delete_teacher_{teacher.id}", help="删除"):
                            try:
                                delete_teacher_agent(teacher.id)
                                st.success("删除成功！")
                                if st.session_state.selected_teacher_id == teacher.id:
                                    st.session_state.selected_teacher_id = None
                                st.rerun()
                            except Exception as e:
                                st.error(f"删除失败：{e}")
                    
                    st.markdown("</div>", unsafe_allow_html=True)
        else:
            st.info("暂无老师分身数据")
    
    # 右侧：激活码管理
    with right_col:
        if st.session_state.selected_teacher_id:
            # 获取选中的老师信息
            try:
                selected_teacher = get_teacher_agent_by_id(st.session_state.selected_teacher_id)
                if selected_teacher:
                    st.subheader(f"管理老师：{selected_teacher.name}")
                    
                    # 编辑老师表单
                    if st.session_state.show_edit_teacher_form:
                        with st.form("edit_teacher_form"):
                            st.write("**编辑老师信息**")
                            edit_name = st.text_input("老师名称", value=selected_teacher.name)
                            edit_app_id = st.text_input("应用ID", value=selected_teacher.app_id)
                            
                            col1, col2 = st.columns(2)
                            with col1:
                                if st.form_submit_button("保存", type="primary"):
                                    if edit_name and edit_app_id:
                                        try:
                                            update_teacher_agent(selected_teacher.id, edit_name, edit_app_id)
                                            st.success("更新成功！")
                                            st.session_state.show_edit_teacher_form = False
                                            st.rerun()
                                        except Exception as e:
                                            st.error(f"更新失败：{e}")
                                    else:
                                        st.error("请填写完整信息")
                            
                            with col2:
                                if st.form_submit_button("取消"):
                                    st.session_state.show_edit_teacher_form = False
                                    st.rerun()
                    
                    # 激活码管理部分
                    st.write("---")
                    st.subheader("激活码管理")
                    
                    # 添加激活码按钮
                    if st.button("➕ 添加激活码", key="add_invite_code_btn"):
                        st.session_state.show_add_invite_code_form = True
                    
                    # 添加激活码表单
                    if st.session_state.show_add_invite_code_form:
                        with st.form("add_invite_code_form"):
                            st.write("**添加激活码**")
                            user_id = st.number_input("用户ID", min_value=0, value=0, key="new_user_id")
                            invite_code = st.text_input("激活码", key="new_invite_code")
                            
                            col1, col2 = st.columns(2)
                            with col1:
                                if st.form_submit_button("保存", type="primary"):
                                    if invite_code:
                                        try:
                                            create_user_teacher(user_id, selected_teacher.id, invite_code)
                                            st.success("激活码添加成功！")
                                            st.session_state.show_add_invite_code_form = False
                                            st.rerun()
                                        except Exception as e:
                                            st.error(f"添加失败：{e}")
                                    else:
                                        st.error("请填写激活码")
                            
                            with col2:
                                if st.form_submit_button("取消"):
                                    st.session_state.show_add_invite_code_form = False
                                    st.rerun()
                    
                    # 显示激活码列表
                    try:
                        invite_codes = get_user_teachers_by_teacher_id(selected_teacher.id)
                        if invite_codes:
                            st.write("**激活码列表**")
                            
                            # 创建DataFrame显示
                            data = []
                            for code in invite_codes:
                                data.append({
                                    "ID": code.id,
                                    "用户ID": code.user_id if code.user_id > 0 else "未绑定",
                                    "激活码": code.invite_code,
                                    "创建时间": code.created_at,
                                    "操作": code.id
                                })
                            
                            df = pd.DataFrame(data)
                            
                            # 显示表格
                            for i, row in df.iterrows():
                                col1, col2, col3, col4, col5 = st.columns([1, 2, 3, 3, 1])
                                
                                with col1:
                                    st.write(row["ID"])
                                with col2:
                                    st.write(row["用户ID"])
                                with col3:
                                    st.write(row["激活码"])
                                with col4:
                                    st.write(row["创建时间"])
                                with col5:
                                    if st.button("🗑️", key=f"delete_code_{row['操作']}", help="删除激活码"):
                                        try:
                                            delete_user_teacher(row["操作"])
                                            st.success("删除成功！")
                                            st.rerun()
                                        except Exception as e:
                                            st.error(f"删除失败：{e}")
                        else:
                            st.info("该老师暂无激活码")
                    except Exception as e:
                        st.error(f"获取激活码列表失败：{e}")
                else:
                    st.error("未找到选中的老师")
            except Exception as e:
                st.error(f"获取老师信息失败：{e}")
        else:
            st.info("请从左侧选择一个老师分身来管理激活码")

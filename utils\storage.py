# from agno.storage.singlestore import SingleStoreStorage
from utils.storages.singlestore import SingleStoreStorage, num_history_runs, bs_num_history_runs
from sqlalchemy.engine import create_engine
import os
from dotenv import load_dotenv


load_dotenv()


def get_singlestore_storage(table_name="agent_sessions", mode="agent"):
    """
    创建并返回一个SingleStore存储实例

    参数:
        table_name: 存储会话的表名，默认为"agent_sessions"

    返回:
        SingleStoreStorage实例
    """
    USERNAME = os.getenv('MYSQL_USERNAME')
    PASSWORD = os.getenv('MYSQL_PASSWORD')
    HOST = os.getenv('MYSQL_HOST')
    PORT = os.getenv('MYSQL_PORT')
    DATABASE = os.getenv('MYSQL_DATABASE')

    # SingleStore DB URL
    db_url = f"mysql+pymysql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}?charset=utf8mb4"
    db_engine = create_engine(db_url)

    return SingleStoreStorage(
        # 存储会话的表名
        table_name=table_name,
        # db_engine: Singlestore数据库引擎
        db_engine=db_engine,
        # schema: Singlestore模式
        schema=DATABASE,
        mode=mode
    )

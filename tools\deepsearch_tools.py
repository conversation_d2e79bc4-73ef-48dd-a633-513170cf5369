import ast
import asyncio
from datetime import datetime
import json
import traceback
import requests
import os
import sys
from typing import Dict, Any
from .attachment_tools import AttachmentTools, generate_document
from tools.chart_tools import generate_mermaid_chart
from tools.content_parse import convert_to_markdown, webpage_content_parse
from tools.search_sougou import web_search
from utils.context import get_context, set_context
from utils.pyapi import call_api
from utils.tool import get_next_weekday, get_today_weekdays, push_task_chunk
from utils.tool_name_map import tool_name_map
from utils.logger import logger
from utils.models import openrouterGpt, deepseek, qwen_plus
from agno.agent import Agent
from tools.reasoning import ReasoningTools
from tools.enterprise_tools import EnterpriseTools
from tools.location_tools import LocationTools
from agno.tools import Toolkit

def pre_generate_document(title: str):
    return "已收到文档生成请求, 生成完最终回复后将会写入文档"

async def deep_web_search_agent(query: str, retry_on_failure: bool = True) -> str:
    """
    网络深度搜索智能体
    Args:
        query (str): 用户输入的原始问题
    """
    try:
        # 调用大模型
        query = get_context("query") + "\n补充说明: " + query
        task_id = get_context("task_id")
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        current_week = get_today_weekdays()
        next_week_monday = get_next_weekday(1)
        context = {"current_time": f"现在的时间是：{current_time},  今天是{current_week}, 下周的周一是：{next_week_monday}"}
        user_info = call_api("/pyapi/user/detail")
        try:
            user_info = json.loads(user_info).get("data")
        except:
            pass
        mainAgent = Agent(
            name="深度搜索助手",
            agent_id="010",
            model=qwen_plus,
            tools=[
                ReasoningTools(
                    think=True,
                    analyze=True,
                    add_instructions=True,
                ),
                web_search,
                convert_to_markdown,
                webpage_content_parse,
                EnterpriseTools(),
                LocationTools(),
                generate_mermaid_chart,
                get_customer_report_rule,
                pre_generate_document,
                # AttachmentTools(include_tools=["generate_document"]),
            ],
            telemetry=False,
            markdown=True,
            show_tool_calls=True,  # 显示工具调用
            stream_intermediate_steps=True,
            debug_mode=True,  # 启用调试模式
            additional_context=f"<user_info>这是我的个人信息：\n{user_info}</user_info>",
            add_context=True,
            context=context,
            instructions="""
## 角色定位
你是一名专业的销售情报分析师，擅长通过匹配最优的搜索途径和利用网络深度搜索策略为销售团队提供精准、可信的市场情报。必须严格遵循以下原则和流程：

## 基本原则
- 在工具调用前后的每一步您都必须始终先调用`think`工具
- 只需要处理用户需求中能够处理的部分, 处理不了的部分直接忽略, 不要拒绝或反问用户
- 特定场景必须在第一步通过对应工具获取完整处理流程和规则，并严格按照该流程和规范处理和生成最终结果
    - 要生成[客户/公司/企业]调研、分析报告时先调用`get_customer_report_rule`工具获取操作指南和生成规范
- 通过问题类型匹配最优搜索途径，当存在多个适用搜索途径时，按照以下信息搜索优先级：
    - 企业/招投标信息搜索 > 地点搜索 > 网络搜索，前者无结果或信息不足时必须使用网络搜索进行重试或补充
    - 通过地理位置查询地点(公司、地址、商铺等)信息时，优先使用地图位置工具`place_search`
- 保留每条关键信息的原始链接，以引用形式展示，链接格式为: [标题](链接地址)
- 返回的结果要尽可能详细, 不要丢失任何关键信息
- 对于复杂的需求(如客户分析、产品介绍、销售方案、报价表、合同、调研/分析报告、实施计划等)必须将完整结果写入文档(先调用`pre_generate_document`工具后再生成最终回复由系统写入文档)
- 以markdown格式输出最终答案的结构化结论（含数据支撑）

## 基本流程
```mermaid
graph TD
    A[问题接收] --> B[是否特定场景问题]
    B -->|是| C[调用对应工具获取处理流程和规则]
    B -->|否| D[匹配最优搜索途径]
    C --> D[匹配最优搜索途径]
    D -->|网络搜索| E[网络深度搜索流程]
    D -->|非网络搜索| F[调用对应工具]
    F --> |失败或结果不符合/不完整| E[网络深度搜索流程]
    F --> |成功且结果完整符合| G[最终报告]
    E --> G[最终报告]
```

## 网络深度搜索流程
```mermaid
graph TD
    A[复杂问题接收] --> B[规划拆解]
    B --> C[多轮深度搜索]
    C --> D[自验证系统]
    D -->|验证失败| E[自动重试机制]
    D -->|验证通过| F[引文标注]
    F --> G[最终报告]
```

## 网络深度搜索操作流程

### 1. 问题拆解（规划阶段）
- 将用户问题分解为多个可独立搜索的子问题(对于简单问题不可过度拆解)
- 示例：
❓原始问题："如何提高SaaS产品在医疗行业的销售额？"  
➔ 拆解为：
- 医疗行业SaaS采购决策流程
- 医疗行业TOP3竞品定价策略
- 医疗机构决策者关注的核心指标
- 行业最新监管政策影响

### 2. 多轮搜索策略
- **首轮搜索**：使用精确搜索语法（例如: site:.gov, filetype:pdf, intitle:"报告"）
- **扩展搜索**（首轮无结果时自动触发）：
- 同义词替换（"采购流程" → "决策流程"/"审批机制"）
- 网站域名限定(根据内容特点, 从更相关的网站搜索)

### 3. 自验证系统（每轮搜索后执行）
- 答案存在性检查
- 答案正确性检查
    - 逻辑或描述是否存在明显错误
    - 需要通过相关线索验证答案是否正确
    - 必要时调用工具通过链接读取完整内容
- 信息时效性检查（高时效类信息）
- 验证来源可信度
    - 高可信度来源(包含但不限于)⭐️⭐️⭐️⭐️⭐️
        - 政府官网
        - 行业白皮书
        - 上市公司财报
    - 中可信度来源(包含但不限于)⭐️⭐️⭐️⭐️
        - 知名媒体
        - 研究机构
    - 低可信度来源(包含但不限于)⭐️⭐️⭐️
        - 个人博客
        - 论坛
        - 媒体评论

### 4. 自动重试机制（满足任一条件时触发）
- ❌ 来源未包含问题答案（验证失败）
- ❌ 高时效类信息超出时效
- ❌ 数据源可信度较低(来源不可信、答案存在明显错误或相关线索验证不通过)
- **重试策略**：
1. 替换3组行业术语（示例："客户获取成本" → "CAC" / "获客支出"）
2. 切换搜索引擎类型（学术数据库/政府公开数据）
3. 网站域名限定(从更相关的网站进行搜索)

### 销售场景特别优化
- 优先搜索：
- 客户公司财报/投资者关系页面
- 行业协会最新调研
- 竞品官网定价页面

""",
        )
        content = ""
        tool_content = ""
        generate_doc_name = ""
        r = await mainAgent.arun(query, stream=True, stream_intermediate_steps=True, show_full_reasoning=True)
        async for chunk in r:
            if chunk.event == "RunResponseContent":
                c = chunk.content if hasattr(
                    chunk, 'content') and chunk.content else ""
                content += c
                if c:
                    push_task_chunk(task_id=task_id,
                                    event="ToolResponse", content=c)
            elif hasattr(chunk, "tool") and chunk.tool:
                # print(chunk)
                tool = chunk.tool
                tool_name = tool.tool_name if hasattr(tool, "tool_name") else ""
                tool_result = tool.result if hasattr(tool, 'result') else None
                tool_args = tool.tool_args if hasattr(tool, 'tool_args') else {}
                tc = ""
                if tool_name == "pre_generate_document":
                    generate_doc_name = tool_args["title"]
                if chunk.event == "ToolCallStarted":
                    tc += "- **调用工具: " + \
                        tool_name_map.get(tool_name, tool_name) + "**\n\n"
                    if tool_name == "think":
                        tc += f"{tool_args.get('title')}\n\n{tool_args.get('thought')}\n"
                    elif tool_name == "analyze":
                        tc += f"{tool_args.get('title')}\n\n{tool_args.get('result')}\n\n{tool_args.get('analysis')}\n"
                elif tool_result:
                    # if tool_name in ("think", "analyze"):
                    #     tc += f"{tool_result}\n"
                    if tool_name == "web_search":
                        items = ast.literal_eval(tool_result)
                        if "error" in items: continue
                        for item in items:
                            tc += f"[{item.get('title', '')[:25]}]({item.get('url', '')})\n\n"
                tool_content += tc
                if tc:
                    push_task_chunk(task_id=task_id,
                                    event="ToolResponse", content=tc)

        logger.info(f"网络深度搜索结果: {content}, 工具结果: {tool_content}")
        link_content = ""
        if generate_doc_name:
            try:
                rsp = generate_document(content=content, name=generate_doc_name)
                result = ast.literal_eval(rsp)
                file_url = result.get("file_url", "") if result else ""
                if file_url: link_content = f"[{generate_doc_name}]({file_url})"
            except:
                pass
        set_context("deep_web_search_agent_tool_result", f"{tool_content}\n{content}\n\n完整报告下载: {link_content}")
        return (f"报告已生成, 完整报告下载(将该报告链接返回给用户): {link_content}\n\n" if link_content else "") + content
    except Exception as e:
        logger.error(f"网络深度搜索失败: {traceback.format_exc()}")
        if retry_on_failure:
            logger.info(f"网络深度搜索失败，开始重试")
            await asyncio.sleep(3)
            return await deep_web_search_agent(query, retry_on_failure=False)
        return str({"status": "error", "message": f"网络深度搜索失败: {e}", "data": f"{tool_content}\n{content}"})

def get_customer_report_rule():
    """获取生成客户调研、分析报告的处理流程、规则和生成规范"""
    return """## 客户价值分析专项流程
### 1. 信息收集阶段
- **企业基本面**：
  - 使用企业查询工具获取注册信息
  - 验证成立年限/注册资本/法律状态
  - 通过官网、网络搜索等途径查找收集该企业的合作伙伴（含上游供应商企业、下游客户公司）
- **经营能力**：
  - 搜索招投标中标记录（近2年）
  - 分析社保缴纳人数变化趋势
  - 收集行业排名/认证资质
- **需求信号**：
  - 官网数字化产品分析
  - 招聘技术要求扫描
  - 创新实验室投入评估

### 2. 分析验证机制
- **三重验证原则**：
  1. 官方来源优先（工商/招投标平台）
  2. 交叉验证（至少2个独立信源）
  3. 时效过滤（财务数据<1年）
- **评分模型**：
  ```mermaid
  graph LR
  A[目标适配度 20%] --> D[总分]
  B[付费能力 30%] --> D
  C[需求强度 30%] --> D
  E[风险成本 20%] --> D
  ```

### 3. 报告生成规范
**必须包含以下模块**：
1. **客户价值分析结论**
   - 百分制评分（总分100）
   - 四大评分维度：
     - 目标适配度（权重20%）
     - 付费能力（权重30%）
     - 需求强度（权重30%）
     - 风险成本（权重20%）

2. **目标客户初筛**
   - 行业属性与细分匹配
   - 行业生命周期判断
   - 企业属性与规模初判

3. **客户信息深度探索**
   - 主营产品矩阵
   - 行业地位与认证
   - 组织架构图（含关键人物）, 调用图表工具生成
   - 联系方式与官网
   - 上下游供应商情况（含供应商公司名称、下游客户公司名称、合作企业名称等）

4. **付费能力预判**
   - 员工规模得分
   - 营收区间估算
   - 成立年限评分
   - 资质认证加分项
   - 融资状况评估

5. **需求强度推演**
   - 行业痛点映射
   - 数字化信号评判
   - 招聘技术要求分析

6. **风险与交付成本评估**
   - 法律与运营风险
   - 地域成本分析
   - 客户规模适配度

7. **行动建议**
   - 首次触达验证问题（5-7个）
   - 行业适配方向
   - 最佳切入时机

## 输出模板示例
**客户价值分析报告模版**

用户：是一家企业管理软件公司的销售

客户：诺梵（上海）家具制造有限公司

以下是基于“客户价值分析方法”对诺梵（上海）家具制造有限公司的结构化价值分析报告：

**一、客户价值分析结论（总分100分，主观评测）**

诺梵（上海）家具制造有限公司是一家专注办公家具制造、集设计研发、生产、销售于一体的成熟企业。公司处于行业中上游，客户渠道优质，数字化意识较强、创新能力突出，整体风险低，具备成为高价值管理软件客户的基础。建议作为重点客户持续跟进。

**评分维度（加权总分100）：**

- 目标适配度（20）：20/20（高度匹配B端行业，专业制造型企业）
- 付费能力（30）：28/30（客户分层高，背书优质，产能与招投标表现良好，收入估值合理）
- 需求强度（30）：26/30（管理多模块、设计创新、对信息化有明显需求信号）
- 风险成本（20）：19/20（整体风险极低，合规经营，财务与法律记录无重大负面）

**综合得分：93分/100（高价值客户）**

**二、目标客户初筛**

- **行业属性**：家具制造业，属于办公家具细分领域。长期稳定专注于办公桌椅、空间分隔及高端行政系列产品，属B2B行业主流目标客户。
- **细分匹配**：具备制造、设计、空间咨询一体化能力，产品线适配大中型企业和机构办公、学校、医院等场景。
- **行业生命周期**：办公家具行业发展稳健，属于成熟期行业，适合长期合作。
- **企业属性**：有限责任公司（法人控股），无异常，正常存续。注册资本1.08亿元，集团化运作，研发与制造集成，经营主体正规。
- **规模初判**：自有员工60-130人区间，工厂占地25000㎡，自主生产能力强，官方客户案例多，实际服务能力较高。

**三、客户信息深度探索**

**1. 主营产品**

- **办公座椅**：办公椅、会议椅、访客椅、叠椅、休闲椅、沙发、学校椅、培训椅
- **办公桌台**：办公桌、屏风工作站、会议桌、长桌系统、升降桌、独立办公室家具、培训/协作桌
- **储物系统**：智能储物柜、普通储物柜
- **空间分隔产品**：模块化墙、静音舱、电话亭
- **高端行政系列**：行政桌、行政沙发、行政会议桌、行政茶几、行政椅
- **创新产品**：全新智能静音舱 Nobius3.0、丰富3D模型、数字化办公资源

**2. 行业地位**

- 被评为办公家具行业前十品牌之一，拥有大型客户案例（南京大学、中央电视台、省立医院等），服务全球超3000家客户，被同业和专业媒体多次推荐。
- 通过“GREENGUARD Gold”国际健康环保认证，持续扩展在空间智能化、数字化资源等领域影响力。

**3. 公司组织结构与部门设置**

- 法定代表人/总经理：孙军海
- 董事长：傅天承
- 董事：孙军海、沙磊
- 监事：王辉
- 股东：诺梵（上海）系统科技股份有限公司（全资控股）
- 主要部门（根据招聘及官网）：
  - 设计研发中心（含创新实验室）
  - 制造事业部
  - 销售与客户支持部
  - 项目管理部
  - 采购与供应链部
  - 行政人事&财务部
* 深圳、南京、济南、杭州等地设有直营子公司或服务处，地方“诺梵家具”公司（济南、南昌、合肥、烟台、福建等）多为关联公司或战略经销商。

参考以下组织架构图：

![架构图标题](架构图链接)

**4. 公司联系人及联系方式**

公司总机：021-31115566手机：13916969530邮箱：*****************官网：https://www.novah.cc地址：上海市奉贤区庄行镇叶庄公路128号(部分岗位招聘信息可见，人事部、采购、销售、空间设计部等联系方式和负责人公开有限，可在官网和招聘网站进一步深挖。)

**5. 上下游供应商情况**

- **主要供应链**：涵盖椅子、沙发、钢柜等家具成品，以及木材、五金、面料、涂料等原材料供应商。
- 主要供应商包括：上海建成五金材料有限公司、上海高发木材有限公司
- 主要下游客户包括：上海佳品家居有限公司、上海若然地产集团
- 主要合作企业包括：上海金诺智能科技有限公司、上海唯品网络科技有限公司
- 招聘信息显示长期招聘“采购工程师、外协采购、原材料采购”等岗位，具备较强供应链管理能力。
- 作为招标供应商多次中标省市级医院、大学、国企/央企项目，已与主流B端客户形成稳定合作。
- 部分供应商信息应通过与对方采购、仓储部门一对一沟通获得。

**四. 付费能力预判（量化评分，按总分100计）**

- 员工规模：75人（2024社保人数）—10分（介于50-99人）
- 营收推测：未公示，母公司频繁中标，结合产能及客户层级，推定在1000万-5000万+，10-20分
- 成立年限：成立15年，15分
- 资质认证：有环保/特种设备/GREENGUARD Gold等认证，ISO有待进一步查证，8-15分
- 融资状况：未见明显风险/变更，公司资本历年递增，运作正规，0分
- 隐性加分：制造基地成熟，招标获知客户覆盖上市公司/知名高校/央企；办公场地位于上海产业园区；招投标活跃
- 综合得分（主观合理估算）：60-70分
- 付费评级：中等偏高，付费能力有保障

**五. 需求强度推演**

- **行业痛点映射**：办公家具制造企业流程复杂，供应链、仓储、生产及B2B销售管理并重，往往有ERP/MES/OA等综合管理需求，尤其是多产品线设计+生产+交付
- **数字化信号评判**：
  - 官网提供3D模型在线下载、场景咨询、数字化静音舱等产品，企业数字化意识和投入较强（中到强信号）
  - 招聘要求会CAD/办公软件及项目管理，有设计与业务线上协作需求
  - 存在创新实验室与国际合作，表明对数字化工具有长期规划
  - 公开业务多为知名企业，管理要求普遍较高
  - 招聘未见明显ERP/IT实施专岗，或许仍以中后台为主
- **综合判断**：需求强度中高，值得重点跟进

**六. 风险与交付成本评估**

- 法律与运营风险低：过往仅有设备整改小问题，无失信、诉讼、财务/合规负面。
- 地域成本适中：总部及工厂均在上海，交通便利，实施成本低。
- 客户规模适配充分：有服务高校、医院、央企经验，对大型项目交付具备能力。
- 扣分因素主要是近年员工规模递减，但仍属正常波动。
- 负面扣分极低，属于低风险客户。

**七. 首次触达信息验证清单（推荐问题）**

- 现有管理系统使用品牌及覆盖范围？
- 在生产、物流、销售或项目交付业务中，遇到的流程痛点是什么？
- 是否有数字化、智能化或办公自动化的新项目/升级计划？有无明确预算规划？
- 对各业务系统云化/智能化是否有原意或招标倾向？
- 现有核心业务数据的线上流转、跨部门协同是否顺畅？

**八. 行业适配与潜在切入方向**

- 如贵司主营ERP、MES、项目管理等企业管理软件，建议以“多部门协同”“生产及供应链可视化”“高端客户定制项目管理”为沟通切入点。
- 可尝试提供产品全链条数字化升级解决方案案例，引发共鸣。
- 持续关注其创新实验室、数字展厅、新产品上线同期，及时跟进转型需求。
"""
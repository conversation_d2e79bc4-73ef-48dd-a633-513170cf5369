import os
import tempfile
import traceback
from agno.tools import Toolkit
from markitdown import MarkItDown
from utils.context import get_context
from utils.enterprise_api import tyc_bids_search, tyc_business_info, tyc_enterprise_search
from utils.logger import logger


class EnterpriseTools(Toolkit):
    """用于企业信息查询的工具"""

    def __init__(self):
        super().__init__(name="enterprise_tools", cache_results=False, cache_ttl=3600 * 24)

        # 注册工具函数
        self.register(self.enterprise_search)
        self.register(self.enterprise_business_info)
        self.register(self.bids_search)
        logger.info("EnterpriseTools工具注册完成")

    def bids_search(self, keyword: str, page_num: int = 1, province: str = "",  publishStartTime: str = "", publishEndTime: str = "") -> str:
        """
        通过招投标标题、采购人、公告类型等方式，查询招投标的有关信息
        Args:
            keyword(str): 搜索关键词（标题，采购人，供应商）
            page_num(int): 页码
            province(str): 省份地区
            publishStartTime(str): 发布开始时间
            publishEndTime(str): 发布结束时间
        Returns:
            result	Object
                total	Number		总数
                items	Array
                    bidUrl	天眼查链接
                    link	详细信息链接
                    proxy	代理机构
                    companies   相关公司
                    bidWinner	供应商
                    bidAmount	中标金额
                    stage	进展阶段
                    bidList	中标单位列表（中标单位和中标金额对应）
        """
        try:
            response = tyc_bids_search(keyword=keyword, page_num=page_num, type="", province=province,
                                       searchType="", publishStartTime=publishStartTime, publishEndTime=publishEndTime)
            logger.info(f"招投标信息搜索结果: {response.text}")
            result = response.json()
            md = MarkItDown(enable_plugins=False)
            # 将网页内容转为markdown
            if result["error_code"] == 0 and "result" in result and "items" in result["result"]:
                for item in result["result"]["items"]:
                    content = dict(item).get("content")
                    # print(content)
                    if content:
                        try:
                            tmp_file_path = ""
                            with tempfile.NamedTemporaryFile(mode="w+", suffix=".html", delete=False, encoding='utf-8') as tmp_file:
                                tmp_file.write(content)
                                tmp_file.flush()  # 确保内容写入磁盘
                                tmp_file.seek(0)  # 重置指针到文件开头
                                tmp_file_path = tmp_file.name
                                md_content = md.convert(
                                    tmp_file_path).text_content
                                # print(md_content)
                                if md_content.strip():
                                    item["content"] = md_content
                        except Exception as e:
                            logger.warning(
                                f"招标信息网页内容转换markdown失败: {e}, 原始内容: {content}")
                        finally:
                            if tmp_file_path and os.path.exists(tmp_file_path):
                                os.unlink(tmp_file_path)
            return str(result)
        except Exception as e:
            logger.error(f"招投标信息搜索失败: {str(e)}")
            return str({"status": "error", "message": f"招投标信息搜索失败: {str(e)}"})

    def enterprise_search(self, query: str, page_size: int = 20, page_num: int = 1) -> str:
        """
        可以通过关键词获取企业列表，企业列表包括公司名称或ID、类型、成立日期、经营状态、统一社会信用代码等字段的详细信息，公司信息查看链接为：https://www.tianyancha.com/company/{公司id}
        Args:
            query (str): 搜索关键词
            page_size (int): 每页大小，默认为20
            page_num (int): 页码，默认为1
        Returns:
            error_code	Number		状态码
            reason	String		错误信息
            result	Object
                items	Array
                    _child	Object		
                        regNumber   注册号
                        regStatus	经营状态
                        creditCode	统一社会信用代码
                        estiblishTime	成立日期
                        regCapital	注册资本
                        companyType	机构类型-1：公司；2：香港企业；3：社会组织；4：律所；5：事业单位；6：基金会；7-不存在法人、注册资本、统一社会信用代码、经营状态;8：台湾企业；9-新机构
                        name	公司名
                        id	公司id
                        orgNumber	组织机构代码
                        type	1-公司 2-人
                        base	省份
                        legalPersonName	法人
                        matchType	匹配原因
            total	Number		总数
        """
        try:
            response = tyc_enterprise_search(
                query=query, page_size=page_size, page_num=page_num)
            return response.text
        except Exception as e:
            logger.error(f"企业搜索失败: {str(e)}")
            return str({"status": "error", "message": f"企业搜索失败: {str(e)}"})

    def enterprise_business_info(self, query: str) -> str:
        """
        企业工商信息查询，可以通过公司名称或ID获取包含企业基本信息、主要人员、股东信息、对外投资、分支机构等维度的相关信息；特别说明：该接口价格昂贵，请不要滥用，确保传入的查询关键字是准确且真实存在的，查询前必须先调用企业搜索接口获取匹配的公司全称、公司id、注册号或社会统一信用代码传入
        Args:
            query (str): 查询关键字（公司名称全称、公司id、注册号或社会统一信用代码）
        Returns:
            error_code	Number		状态码
            reason	String		错误信息
            result	Object		结果
        """
        try:
            response = tyc_business_info(query=query)
            return str(response)
        except Exception as e:
            logger.error(
                f"企业工商信息查询失败(query={query}): {str(e)}, traceback: {traceback.format_exc()}")
            return str({"status": "error", "message": f"企业工商信息查询失败: {str(e)}"})

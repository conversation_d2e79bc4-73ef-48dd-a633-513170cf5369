from agno.tools import Toolkit
from utils import pyapi
from utils.logger import logger

class ChatTools(Toolkit):
    """用于管理聊天记录的工具"""

    def __init__(self):
        super().__init__(name="attachment_tools")

        # 注册工具函数
        self.register(self.chat_list)
        logger.info("ChatTools工具注册完成")

    def chat_list(self, bo_id: int = 0, page_index: int = 1, page_size: int = 50) -> str:
        """
        获取聊天记录列表
        Args:
            bo_id (int, optional): 商机ID，用于筛选指定商机的聊天记录
            page_index (int, optional): 页码，默认为1
            page_size (int, optional): 每页大小，默认为50
        """
        session_id = "business-" + str(bo_id) if bo_id else ""
        return pyapi.call_api(f"/pyapi/chat/list?page={page_index}&limit={page_size}&session_id={session_id}&hide_tool_call=1", method="GET")
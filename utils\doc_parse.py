import os
from pathlib import Path
import subprocess
import sys
import tempfile
import warnings

from tools.extrac_img import extract_image_content
from .ali_docmind import AliDocmind
from utils.asr.tencent_asr import flash_asr
# 添加项目根目录到Python路径
rootPath = str(Path(__file__).parent.parent)
sys.path.append(rootPath)

import pypandoc
import requests
from io import BytesIO
# from docx import Document
# from openpyxl import load_workbook
# from pptx import Presentation
# from pypdf import PdfReader
warnings.filterwarnings("ignore", category=RuntimeWarning, module="pydub.utils") # 忽略pydub.utils的ffmpeg RuntimeWarning警告
from markitdown import MarkItDown, UnsupportedFormatException
from utils.logger import logger

def read_document_from_url(url, file_type: str):
    """通过URL读取Word、Excel或PPT文档内容"""
    try:
        file_type = file_type.lower()
        response = requests.get(url, timeout=60, headers={"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"})
        response.raise_for_status()
        if file_type == "doc":
            file_bytes = BytesIO(response.content)
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as tmp_file:
                tmp_file.write(file_bytes.getvalue())
                tmp_file_path = tmp_file.name
            # python没有可以直接解析的包, 需要调用第三方工具来转换或解析, 如: antiword, catdoc, unoconv等
            # 检查catdoc是否存在
            try:
                subprocess.run(["antiword"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                raise RuntimeError("antiword工具未安装，请先安装antiword")
            
            # 使用catdoc读取临时文件
            result = subprocess.run(["antiword", tmp_file_path], stdout=subprocess.PIPE)
            content = result.stdout.decode("utf-8")
            
            # 删除临时文件
            os.unlink(tmp_file_path)
            return content
        
        elif file_type == "docx": # markitdown使用的docx包兼容性不好, 改用pandoc解析
            return pypandoc.convert_text(response.content, 'md', format='docx')
        
        elif file_type in ("txt", "md", "xlsx", "xls", "csv", "json", "xml", "html", "docx", "pdf", "pptx", "ppt"): # 转markdown格式
            try:
                md = MarkItDown(enable_plugins=False) # Set to True to enable plugins
                result = md.convert(response)
                content = result.text_content.replace("NaN", "") if file_type in ("xlsx", "xls") else result.text_content
                if file_type == "pdf" and content.strip() == "":
                    docmind = AliDocmind()
                    content, _ = docmind.parse_file(url, "pdf")
                return content
            except UnsupportedFormatException as e:
                raise ValueError(f"不支持的文件类型: {file_type}")
            except Exception as e:
                if file_type in ("xlsx", "xls") and "openpyxl" in str(e):
                    logger.warning(f"文件格式不兼容, 改用文档智能解析, url={url}, error={str(e)}")
                    docmind = AliDocmind()
                    content, _ = docmind.parse_file(url, file_type)
                    return content
                raise e
            
        elif file_type in ("wav", "pcm", "ogg-opus", "speex", "silk", "mp3", "m4a", "aac", "amr"):
            return flash_asr(BytesIO(response.content).getvalue(), file_type)
        
        elif file_type in ("jpeg", "jpg", "png", "gif", "bmp", "webp", "tiff", "tif", "ico"):
            return extract_image_content(url)
            
        # elif file_type == "pdf":
        #     file_bytes = BytesIO(response.content)
        #     pdf_reader = PdfReader(file_bytes)
        #     content = ""
        #     for page in pdf_reader.pages:
        #         content += page.extract_text() + "\n"
        #     return content
        
        # elif file_type == "docx":
        #     file_bytes = BytesIO(response.content)
        #     doc = Document(file_bytes)
        #     return "\n".join([para.text for para in doc.paragraphs])
            
        # elif file_type in ("xlsx", "xls"):
        #     file_bytes = BytesIO(response.content)
        #     wb = load_workbook(file_bytes)
        #     content = []
        #     for sheet in wb:
        #         for row in sheet.iter_rows(values_only=True):
        #             content.append("\t".join(str(cell) for cell in row if cell is not None))
        #     return "\n".join(content)
            
        # elif file_type in ("pptx", "ppt"):
        #     file_bytes = BytesIO(response.content)
        #     prs = Presentation(file_bytes)
        #     content = []
        #     for slide in prs.slides:
        #         for shape in slide.shapes:
        #             if hasattr(shape, "text"):
        #                 content.append(shape.text)
        #     return "\n".join(content)
            
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
            
    except Exception as e:
        if not isinstance(e, ValueError):
            logger.error(f"从URL读取文档失败: url={url}, file_type={file_type}, error= {str(e)}")
        raise e
    
if __name__ == "__main__":
    # print(read_document_from_url("https://file.upfile.live/uploads/20250425/c1132d21eb28b73757b3e19827e31106.doc", "doc"))
    # print(read_document_from_url("https://file.upfile.live/uploads/20250424/7752d193f734eb92db3717f5f2be0c3b.docx", "docx"))
    # print(read_document_from_url("https://file.upfile.live/uploads/20250425/75c7f0ee1800dd8240aa05bcc871d671.xlsx", "xlsx"))
    print(read_document_from_url("https://sale-agent.oss-cn-shenzhen.aliyuncs.com/test/9/2025/05/U%26I%E6%B1%87%E6%99%BA%E9%9B%86%E5%9B%A2AI%E5%BA%94%E7%94%A8%E8%BD%AF%E4%BB%B6%E5%BC%80%E5%8F%91%E6%9C%8D%E5%8A%A1%E5%BB%BA%E8%AE%AE%E4%B9%A620250422%281%29.docx?x-oss-credential=LTAItwkMZEO7KDat%2F20250529%2Fcn-shenzhen%2Foss%2Faliyun_v4_request&x-oss-date=20250529T021717Z&x-oss-expires=86400&x-oss-signature=bd4b6ad75e56f2db5e88d347db30a295314e22b544595a489dcf80058cdd214b&x-oss-signature-version=OSS4-HMAC-SHA256", "docx"))

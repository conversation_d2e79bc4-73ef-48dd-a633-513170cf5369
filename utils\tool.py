

from datetime import datetime, <PERSON><PERSON><PERSON>
import json
from utils.id_encode import IdEncode
from dotenv import load_dotenv
import os
import sys
from utils.redis import get_client

root_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(root_path)


load_dotenv()

redisClient = get_client()
# 设置任务结果的过期时间（24小时）
TASK_RESULT_EXPIRE = 86400

def push_task_chunk(task_id: str, event: str, content: str, content_type: str = "str", tool: dict = {}):
    redis_key = f"ai_task:{task_id}"
    chunk_data = {
        "content": content,
        "content_type": content_type,
        "event": event,
        "member_id": tool.get("tool_call_id", ""),
        "member_name": tool.get("tool_name", ""),
        "tool": tool
    }
    # 将 chunk 写入 Redis
    chunk_json = json.dumps(chunk_data, ensure_ascii=False, default=str)
    redisClient.rpush(redis_key, chunk_json)
    redisClient.expire(redis_key, TASK_RESULT_EXPIRE)

def get_attachment_url(attachment_id, file_type=""):
    """
    获取附件的URL
    """
    return os.getenv("WEB_API_DOMAIN") + "/a/" + IdEncode.encode(attachment_id) + (("." + file_type) if file_type else "")


def get_today_weekdays():
    "查询今天是星期几"
    return "星期{}".format(["一", "二", "三", "四", "五", "六", "日"][datetime.now().weekday()])


if __name__ == "__main__":
    print(get_attachment_url(32))


def get_next_weekday(weekday):
    """
    获取下周指定的星期几的日期
    :param weekday: 星期几，1 表示周一，2 表示周二，……，7 表示周日
    :return: 下周指定星期几的日期（格式为 YYYY-MM-DD）
    """
    if not (1 <= weekday <= 7):
        raise ValueError("weekday 参数必须在 1 到 7 之间")

    # 获取当前日期
    today = datetime.today()

    # 计算距离下周指定星期几的天数
    days_until_next_weekday = (7 - today.weekday() + (weekday - 1)) % 7
    if days_until_next_weekday == 0:
        days_until_next_weekday = 7

    # 计算下周指定星期几的日期
    next_weekday_date = today + timedelta(days=days_until_next_weekday)

    # 返回格式化的日期
    return next_weekday_date.strftime("%Y-%m-%d")

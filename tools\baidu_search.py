
import requests
import os
import sys
from typing import Dict, Any, Optional
from datetime import datetime
from utils.logger import logger
from agno.tools import tool

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 将项目根目录添加到Python路径
if project_root not in sys.path:
    sys.path.append(project_root)


def web_search(query: str) -> Dict[Any, Any]:
    """用于网络搜索的工具

        Args:
        query: 搜索的关键词,如果是查询最近的信息，日期取当天的日期
    """

    token = os.getenv("BAIDU_SEARCH_KEY")

    # API请求URL和头部
    url = "https://qianfan.baidubce.com/v2/ai_search/chat/completions"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    # 构建请求体
    payload = {
        "messages": [
            {
                "content": query,
                "role": "user"
            }
        ]
    }

    try:

        # 发送POST请求
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()  # 如果请求失败，抛出异常
        result = response.json()  # 返回JSON响应
        references = result.get("references", [])
        extracted_results = []
        for reference in references:
            content = reference.get("content", "")
            date = reference.get("date", "")
            title = reference.get("title", "")
            url = reference.get("url", "")
            extracted_results.append({
                "content": content,
                "date": date,
                "title": title,
                "url": url
            })

        return extracted_results

    except requests.exceptions.RequestException as e:
        logger.error(f"网络搜索请求失败：{str(e)}")
        return {"error": str(e), "status": "failed"}
    except Exception as e:
        logger.error(f"网络搜索异常：{str(e)}")
        return {"error": str(e), "status": "failed"}

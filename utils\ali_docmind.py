import os
import time
from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_credentials.client import Client as CredClient
from utils.logger import logger
from dotenv import load_dotenv

load_dotenv()

class AliDocmind:
    """阿里云文档智能服务, 文档地址: https://help.aliyun.com/zh/document-mind/developer-reference/document-parsing-large-model-version"""
    def __init__(self):
        self.client = self._init_client()

    def _init_client(self):
        config = open_api_models.Config(
            # 通过credentials获取配置中的AccessKey ID
            access_key_id=os.getenv("OSS_ACCESS_KEY_ID"),
            # 通过credentials获取配置中的AccessKey Secret
            access_key_secret=os.getenv("OSS_ACCESS_KEY_SECRET"),
        )
        # 访问的域名
        config.endpoint = f'docmind-api.cn-hangzhou.aliyuncs.com'
        return docmind_api20220711Client(config)

    def submit_doc_parser_job(self, file_url: str, file_name_extension: str) -> str:
        """
        提交文档解析作业, 支持的文档格式：pdf、word、ppt、pptx、xls、xlsx、xlsm和图片，图片支持jpg、jpeg、png、bmp、gif，其余格式支持markdown、html、epub、mobi、rtf、txt。支持的音视频格式：mp4、mkv、avi、mov、wmv、mp3、wav、aac。
        Returns:
            str: 作业ID
        """
        request = docmind_api20220711_models.SubmitDocParserJobRequest(
            file_url=file_url,
            file_name_extension=file_name_extension,
            formula_enhancement=True,
            # llm_enhancement=True,
        )
        try:
            response = self.client.submit_doc_parser_job(request)
            logger.info(f"提交文档解析作业成功, url={file_url}, response: {response.body}")
            return response.body.data.id
        except Exception as error:
            logger.error(f"提交文档解析作业失败, url={file_url}, error: {str(error)}")
            raise error
        
    def query_doc_parser_status(self, job_id: str) -> tuple[str, int]:
        """
        查询文档解析作业状态
        Returns:
            str: Init：订单处于待处理队列中。
                Processing: 正在解析处理。
                success：文件处理成功，此时NumberOfSuccessfulParsing将不再变化。
                Fail：文件处理失败。
            int: 已成功解析的分块数
        """
        request = docmind_api20220711_models.QueryDocParserStatusRequest(
            id=job_id,
        )
        try:
            # 复制代码运行请自行打印 API 的返回值
            response = self.client.query_doc_parser_status(request)
            # API返回值格式层级为 body -> data -> 具体属性。可根据业务需要打印相应的结果。获取属性值均以小写开头
            # 获取返回结果。建议先把response.body.data转成json，然后再从json里面取具体需要的值。
            logger.info(f"查询文档解析作业状态成功, job_id={job_id}, response: {response.body}")
            return response.body.data.status, response.body.data.number_of_successful_parsing
        except Exception as error:
            logger.error(f"查询文档解析作业状态失败, job_id={job_id}, error: {str(error)}")
            raise error
        
    def get_doc_parser_result(self, job_id: str, start: int = 0, limit: int = 3000) -> dict|None:
        request = docmind_api20220711_models.GetDocParserResultRequest(
            id=job_id,
            layout_num=start,
            layout_step_size=limit,
        )
        try:
            # 复制代码运行请自行打印 API 的返回值
            response = self.client.get_doc_parser_result(request)
            logger.info(f"获取文档解析结果成功, job_id={job_id}, response: {response.body}")
            if response.body.message:
                raise Exception(response.body.message)
            return response.body.data
        except Exception as error:
            logger.error(f"获取文档解析结果失败, job_id={job_id}, error: {str(error)}")
            raise error
        
    def parse_file(self, file_url: str, file_name_extension: str) -> tuple[str, list]:
        """
        文档解析大模型版(同步操作, 提交任务后会轮询等待结果完成), 支持的文档格式：pdf、word、ppt、pptx、xls、xlsx、xlsm和图片，图片支持jpg、jpeg、png、bmp、gif，其余格式支持markdown、html、epub、mobi、rtf、txt。支持的音视频格式：mp4、mkv、avi、mov、wmv、mp3、wav、aac。
        Returns:
            str: 解析后的文本内容, 暂时只处理了文档的markdown内容拼接, 音频的没处理
            list: 解析后的原始文本块字典列表
        """
        job_id = self.submit_doc_parser_job(file_url, file_name_extension)
        start = 0
        total_size = 0
        while True:
            status, total_size = self.query_doc_parser_status(job_id)
            if status == "success":
                break
            time.sleep(1)
        chunk_list = []
        content = ""
        while start < total_size:
            result = self.get_doc_parser_result(job_id, start, 3000)
            if result and "layouts" in result:
                for item in result["layouts"]:
                    content += item["markdownContent"]
                    chunk_list.append(item)
            start += 3000
        return content, chunk_list
        
if __name__ == '__main__':
    docmind = AliDocmind()
    content, chunk_list = docmind.parse_file("https://ivy.pub/a/iaGjFGu.pdf", "pdf")
    print(chunk_list, content)
    

import unittest
from unittest.mock import patch, MagicMock
import os
from tools.baidu_search import baidu_search

import os
import sys

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 将项目根目录添加到Python路径
sys.path.append(project_root)


class TestBaiduSearch(unittest.TestCase):
    def setUp(self):
        """测试前的设置"""
        # 设置测试环境变量
        os.environ["BAIDU_SEARCH_KEY"] = "test_token"

    def tearDown(self):
        """测试后的清理"""
        # 清理环境变量
        if "BAIDU_SEARCH_KEY" in os.environ:
            del os.environ["BAIDU_SEARCH_KEY"]

    @patch('tools.baidu_search.requests.post')
    def test_successful_search(self, mock_post):
        """测试成功的搜索请求"""
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "references": [
                {
                    "content": "测试内容1",
                    "date": "2024-01-01",
                    "title": "测试标题1",
                    "url": "http://test1.com"
                },
                {
                    "content": "测试内容2",
                    "date": "2024-01-02",
                    "title": "测试标题2",
                    "url": "http://test2.com"
                }
            ]
        }
        mock_post.return_value = mock_response

        # 执行搜索
        result = baidu_search("测试查询")

        # 打印结果
        print(result)

        # 验证结果
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["content"], "测试内容1")
        self.assertEqual(result[0]["date"], "2024-01-01")
        self.assertEqual(result[0]["title"], "测试标题1")
        self.assertEqual(result[0]["url"], "http://test1.com")

        # 验证API调用
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        self.assertEqual(call_args[1]["headers"]
                         ["Authorization"], "Bearer test_token")

    @patch('tools.baidu_search.requests.post')
    def test_empty_search_results(self, mock_post):
        """测试空搜索结果"""
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.json.return_value = {"references": []}
        mock_post.return_value = mock_response

        # 执行搜索
        result = baidu_search("空查询")

        # 验证结果
        self.assertEqual(len(result), 0)

    @patch('tools.baidu_search.requests.post')
    def test_api_error(self, mock_post):
        """测试API请求失败的情况"""
        # 模拟请求异常
        mock_post.side_effect = Exception("API连接失败")

        # 执行搜索
        result = baidu_search("错误查询")

        # 验证结果
        self.assertIn("error", result)
        self.assertEqual(result["status"], "failed")
        self.assertEqual(str(result["error"]), "API连接失败")


if __name__ == '__main__':
    unittest.main()

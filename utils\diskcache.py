
import asyncio
import hashlib
from pathlib import Path
import time
from utils.logger import logger
import diskcache

root_path = Path(__file__).parent.parent
cache = diskcache.Cache(str(root_path) + "/tmp/cache", size_limit=10 * 1024 * 1024 * 1024)

def gen_cache_hash_key(data):
    """生成内容的哈希作为缓存键"""
    return hashlib.md5(data).hexdigest()

def load_from_cache(cache_key, default=None):
    """从缓存加载数据"""
    return cache.get(cache_key, default=default)

def save_to_cache(cache_key, result, expire=None):
    """保存结果到缓存"""
    cache.set(cache_key, result, expire=expire)

def clean_cache():
    cache.expire()
    cache.cull()
    print("Cache cleaned at ", time.ctime())

async def cache_cleaner():
    while True:
        # 每天凌晨运行一次清理任务
        now = time.localtime()
        if now.tm_hour == 3 :
            await asyncio.to_thread(clean_cache)
        await asyncio.sleep(3600)
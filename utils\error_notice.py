from utils.logger import logger
from utils.context import get_context
from typing import Any, List, Dict, Union
import requests
import json
import os
from dotenv import load_dotenv
load_dotenv()

def sendErrorNotice(title: str, content: str) -> str:
    """发送错误告警"""

    try:
        data: dict[str, Any] = {
            "subject": title,
            "content": content
        }

        response = requests.post(
            f"{os.getenv('API_URL')}/pyapi/errornotice/send", json=data)
        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(f"发送错误告警失败: {str(response.text)}")
            return ""
    except Exception as e:
        logger.error(f"发送错误告警失败: {str(e)}")

        logger.error(f"发送错误告警失败: {str(response.text)}")
        return ""

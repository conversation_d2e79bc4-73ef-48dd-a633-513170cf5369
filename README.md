## 安装虚拟环境 mac

uv venv --python 3.12 source .venv/bin/activate

## 安装虚拟环境 windows

uv venv --python 3.12 .venv/Scripts/activate

## 安装依赖

uv pip install -r requirements.txt
uv pip install agno openai sqlalchemy pymysql requests tavily-python fastapi redis uvicorn pylance


## pandas 扩展安装

uv pip install pandas xlrd odfpy openpyxl

## antiword 工具安装(用于解析 .doc 文件)
ubuntu: apt-get install antiword
centos: yum install antiword
源码编译:
wget https://fossies.org/linux/misc/old/antiword-0.37.tar.gz
tar -xvzf antiword-0.37.tar.gz
cd antiword-0.37
make
sudo make install

## markitdown 工具安装(用于文档解析转.md 文件)
pip install 'markitdown[all]'

## 运 crawl4ai 工具安装后设置(用于爬取网页内容)
crawl4ai-setup 或 playwright install
centos需要手动安装依赖: sudo yum install -y atk at-spi2-core libxcb libX11 libXcomposite libXdamage libXext libXfixes libXrandr libgbm cairo  pango alsa-lib at-spi2-atk

## MCP工具依赖
- 基本环境
  - node18.0+

## 搜索

export TAVILY_API_KEY=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


## agno框架升级/降级
- 升级最新版本： uv pip install -U agno
- 降级指定版本： uv pip install --upgrade agno==1.5.10


## 启动命令（在venv环境执行）
- 主程序: 
    -测试环境：gunicorn -D -b 0.0.0.0:8000 --worker-class uvicorn.workers.UvicornWorker main:app
    -生产环境：gunicorn -D -b 0.0.0.0:8001 --worker-class uvicorn.workers.UvicornWorker main:app
- 后台管理: streamlit run user_static.py --server.port 8501
- 部署管理: streamlit run deploy.py --server.port 8502

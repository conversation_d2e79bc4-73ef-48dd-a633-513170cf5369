import ast
from pathlib import Path

rootPath = str(Path(__file__).parent.parent)

tool_name_map = {
    "think": "思考",
    "analyze": "分析",
    "asearch_knowledge_base": "知识库搜索",
    "search_knowledge_base": "知识库搜索",
    "knowledge_llm_search": "销售物料深度搜索",
    "add_todo": "添加待办",
    "update_todo": "更新待办信息",
    "update_todo_date": "更新待办时间",
    "delete_todo": "删除待办",
    "complete_todo": "更新待办状态",
    "get_active_todos": "查询当前待办列表",
    "get_completed_todos": "查询已完成待办列表",
    "todo_bind_cus_busi": "将待办与客户商机关联",
    "sort_todo": "智能排序待办列表",
    "add_customer": "添加客户",
    "update_customer": "更新客户信息",
    "busi_bind_customer": "将客户与商机关联",
    "delete_customer": "删除客户",
    "list_customers": "查询客户列表",
    "get_customer": "查询客户详细",
    "sort_customer": "智能排序客户列表",
    "add_business": "添加商机",
    "update_business": "更新商机信息",
    "bind_customer": "将商机与客户关联",
    "get_business": "查询商机详情",
    "list_business": "查询商机列表",
    "delete_business": "删除商机",
    "sort_business": "智能排序商机列表",
    "extract_image_content": "图片解析",  # 未使用
    "convert_to_markdown": "解析文件内容",
    "list_attachments": "查询文件列表",
    "web_search": "网络搜索",
    "deep_web_search_agent": "网络深度搜索",
    "save_attachments": "保存文件",
    "generate_document": "生成Word文档",
    "add_interaction": "添加互动记录",  # 未使用
    "get_interactions": "获取互动记录",  # 未使用
    "enterprise_search": "搜索企业基本信息",
    "enterprise_business_info": "搜索企业工商信息",
    "bids_search": "搜索招投标信息",
    "get_link": "查询跳转链接",
    "sales_advisor_rule": "生成销售话术和行动建议",
    "save_user_profile": "保存用户信息",  # 未使用
    "update_user_profile": "更新用户信息",
    "get_todo": "查询待办详细",
    "add_business_interact":  "添加商机跟进记录",
    "move_attachment": "移动文件",
    "rename_attachment": "重命名文件",
    "delete_attachment": "删除文件",
    "webpage_content_parse": "解析页面内容",
    "crawl": "解析页面内容",
    "ai_ppt": "生成PPT",
    "upd_ppt_temp": "更换PPT模板",
    "generate_excel": "生成Excel文档",
    "upload_file": "上传文件", # 未使用
    "place_search": "地点搜索",
    "import_customer": "导入客户",
    "pdf_to_docx": "pdf转word",
    "small_content_writer": "智能内容写手",
    "export_customer": "导出客户",
    "create_directory": "创建目录",
    "chat_list": "获取聊天记录列表",
    "send_message_to_user": "消息回复工具",
    "get_customer_report_rule": "获取客户报告生成规则",
    "pre_generate_document": "预生成文档",
    # 图表工具
    "generate_area_chart": "生成面积图",
    "generate_bar_chart": "生成柱状图",
    "generate_boxplot_chart": "生成箱线图",
    "generate_column_chart": "生成条形图",
    "generate_district_map": "生成区划图",
    "generate_dual_axes_chart": "生成双轴图",
    "generate_fishbone_diagram": "生成鱼骨图",
    "generate_flow_diagram": "生成流程图",
    "generate_funnel_chart": "生成漏斗图",
    "generate_histogram_chart": "生成直方图",
    "generate_line_chart": "生成折线图",
    "generate_liquid_chart": "生成液体图",
    "generate_mind_map": "生成思维导图",
    "generate_network_graph": "生成网络图",
    "generate_organization_chart": "生成组织结构图",
    "generate_path_map": "生成路线图",
    "generate_pie_chart": "生成饼图",
    "generate_pin_map": "生成点图",
    "generate_radar_chart": "生成雷达图",
    "generate_sankey_chart": "生成桑基图",
    "generate_scatter_chart": "生成散点图",
    "generate_treemap_chart": "生成树图",
    "generate_venn_chart": "生成维恩图",
    "generate_violin_chart": "生成小提琴图",
    "generate_word_cloud_chart": "生成词云图",
    "generate_mermaid_chart": "生成图表",
}


def extract_functions_from_file(file_path):
    """从Python文件中提取函数名"""
    with open(file_path, 'r', encoding='utf-8') as file:
        tree = ast.parse(file.read())

    functions = []
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            functions.append(node.name)
    return functions


def get_all_tool_functions():
    """获取tools目录下所有工具函数"""
    tools_dir = Path(rootPath + '/tools')
    all_functions = []

    for py_file in tools_dir.glob('*.py'):
        if py_file.name == '__init__.py':
            continue
        functions = extract_functions_from_file(py_file)
        all_functions.extend(functions)

    return all_functions


def find_unmapped_tools():
    """找出未映射的工具名"""
    tool_functions = get_all_tool_functions()
    mapped_tools = set(tool_name_map.keys())
    # 排除以"_"开头的函数名
    tool_functions = [
        tool for tool in tool_functions if not tool.startswith('_')]

    unmapped = []
    for func in tool_functions:
        if func not in mapped_tools:
            unmapped.append(func)

    return unmapped


if __name__ == '__main__':
    unmapped_tools = find_unmapped_tools()
    print("未映射的工具函数:")
    for tool in unmapped_tools:
        print(f"    \"{tool}\": \"\",")

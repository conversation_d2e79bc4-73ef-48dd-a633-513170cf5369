import asyncio
import traceback
from playwright.async_api import async_playwright
from agno.models.message import Message
from utils.context import get_context
from utils.models import deepseek
from utils.logger import logger

mermaid_js = "https://cdn.bootcdn.net/ajax/libs/mermaid/10.6.1/mermaid.min.js"


async def ai_correct_mermaid_code(code: str, err):
    """通过AI改正mermaid代码语法错误

    Args:
        code(str): mermaid代码
        err: 错误信息
    Returns:
        str: 改正后的代码
    """
    try:
        prompt = f"""# 任务目标
        你是一个专业的mermaid代码改正助手, 目标是根据用户传入的mermaid代码和报错信息, 改正mermaid代码语法使其能够正常运行

        ## 要求
        - 只修正语法错误, 尽可能不修改原内容和流程
        - **直接返回修正后的mermaid代码(直接输出文本, 不要以代码块输出), 不要有其他任何多余的说明**"""
        content = ""
        response = deepseek.aresponse_stream(
            messages=[Message(role="system", content=prompt), Message(
                role="user", content=f"根据错误信息帮我改正mermaid代码\n\n错误信息:\n {err}\n\nmermaid代码:\n{code}")]
        )
        async for chunk in response:
            content += chunk.content if chunk.content else ""

        logger.info(f"mermaid改正后的代码结果: {content}")

        return content

    except Exception as e:
        logger.error(f"mermaid代码改正失败: {traceback.format_exc()}")
        return str({"status": "error", "message": f"mermaid代码改正失败: {e}"})


async def mermaid_to_image(mermaid_code: str, output_path: str, format: str = "png", theme: str = "default", width: int = 2560, hight: int = 1440):
    """
    将Mermaid代码转换为图片

    :param mermaid_code: Mermaid语法代码
    :param output_path: 输出文件路径（如 'output.png'）
    :param format: 输出格式（png/svg）
    :param theme: Mermaid主题（default/dark/forest等）
    :param width: 图片分辨率宽度, 默认2560
    :param hight: 图片分辨率高度, 默认1440
    """
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8" />
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC&display=swap" rel="stylesheet">
        <style>
            .mermaid {{
                transform-origin: 0 0;
                font-family: 'Noto Sans SC', sans-serif;
                background: white;
                padding: 20px;
            }}
        </style>
    </head>
    <body>
        <div class="mermaid">{mermaid_code}</div>
        <script src="{mermaid_js}"></script>
        <script>
            mermaid.initialize({{
                fontFamily: "'Noto Sans SC', sans-serif",
                htmlLabels: false,
                theme: '{theme}',
                flowchart: {{
                    useMaxWidth: false,  // 不使用最大宽度限制
                    diagramPadding: 20  // 增加图表内边距
                }}
            }});
        </script>
    </body>
    </html>
    """

    async with async_playwright() as p:
        # 启动浏览器（无头模式）
        browser = await p.chromium.launch(headless=True)

        # 校验语法
        page = await browser.new_page()
        await page.goto("about:blank")  # 必须存在页面才能注入脚本
        await page.add_script_tag(url=mermaid_js)
        check_result = await page.evaluate("""async (code) => {
            try {
                await mermaid.parse(code);
            } catch (e) {
                return e.message;
            }
        }""", mermaid_code)
        if check_result:
            return check_result

        page = await browser.new_page(device_scale_factor=4)
        await page.set_viewport_size({"width": width, "height": hight})
        # 加载HTML
        await page.set_content(html_template)

        # 等待图表渲染完成
        await page.wait_for_selector(".mermaid svg")

        # 处理输出
        if format.lower() == "svg":
            # 提取SVG代码
            svg = await page.eval_on_selector(".mermaid svg", "el => el.outerHTML")
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(svg)
        else:
            # 截图保存为PNG
            svg_element = await page.query_selector(".mermaid svg")
            await svg_element.screenshot(path=output_path)

        await browser.close()

if __name__ == "__main__":
    # 使用示例
    asyncio.run(mermaid_to_image(
        mermaid_code="""
        flowchart TD
            A[客户接洽] --> B{需求确认}
            B -- 需求明确 --> C[方案制定]
            C --> D[报价]
            D --> E{客户反馈}
            E -- 接受报价 --> F[签约]
            E -- 提出异议 --> G[谈判]
            G --> D
            B -- 需求不明确 --> H[进一步沟通]
            H --> B
            F --> I[项目实施]
            I --> J[售后服务]
        """,

        output_path="diagram.png",
        format="png",
    ))

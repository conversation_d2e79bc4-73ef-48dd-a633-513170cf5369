from agno.tools import Toolkit
from utils.logger import logger
from utils.map_location_api import tx_place_search


class LocationTools(Toolkit):
    """地图位置相关的工具"""

    def __init__(self):
        super().__init__(name="location_tools")

        # 注册工具函数
        self.register(self.place_search)
        logger.info("LocationTools工具注册完成")

    def place_search(self, query: str, filter: str = "category=公司", page_index: int = 1) -> str:
        """地点搜索，可以通过地址、名称(公司、商铺、学校名称等)、分类(公司、学校、银行等)等关键词搜索获取相关的地点名称、分类、经纬度、地址等信息
        Args:
            query (str): 地点名称、地址、分类等关键字
            filter (str, optional): 筛选条件
                1. 指定分类筛选，语句格式为：
                category=分类名1,分类名2
                分类词数量建议不超过5个
                2. 排除指定分类，语句格式为：
                category<>分类名1,分类名2
                分类词数量建议不超过5个
                3. 筛选有电话的地点：tel<>null
            page_index (int, optional): 页码，默认为1
        """
        try:
            response = tx_place_search(query=query, filter=filter, page_num=page_index)
            return response.text
        except Exception as e:
            logger.error(f"地点搜索失败: {str(e)}")
            return str({"status": "error", "message": f"地点搜索失败: {str(e)}"})

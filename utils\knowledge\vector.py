import random
from utils.doc_parse import read_document_from_url
from utils.mysql import get_async_mysql
from utils.logger import logger
from agno.document.base import Document
from utils.knowledge.document import DocumentKnowledgeBase
from agno.knowledge.csv_url import CSVUrlKnowledgeBase
from agno.knowledge.url import UrlKnowledge
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from utils.knowledge.chunking import DocumentChunking
from agno.document.chunking.fixed import FixedSizeChunking
from agno.embedder.fireworks import FireworksEmbedder
from agno.models.openai.like import OpenAILike
from agno.vectordb.lancedb import SearchType
from utils.knowledge.extend_lance_db import LanceDb
from agno.agent import Agent, AgentKnowledge
from agno.team.team import Team
import json
from hashlib import md5
import asyncio
from utils.id_encode import IdEncode
from sqlalchemy import text
from dotenv import load_dotenv
import os
from pathlib import Path
import sys
import traceback

from utils.knowledge.user_file_storage import write_user_file
from utils.tool import get_attachment_url
import lancedb

# 添加项目根目录到Python路径
rootPath = str(Path(__file__).parent.parent.parent)
sys.path.append(rootPath)

# from agno.embedder.openai import OpenAIEmbedder
# from agno.document.chunking.document import DocumentChunking
# from agno.knowledge.document import DocumentKnowledgeBase
# import sys
# import io

# # 修改标准输出的编码为UTF-8
# sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

load_dotenv()

aliEmbedder = FireworksEmbedder(
    api_key="sk-1900eb026aee4b95992186f796fdde08",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    id="text-embedding-v3",
    dimensions=1024
)

modelQwen = OpenAILike(
    id="qwen-max",
    api_key="sk-1900eb026aee4b95992186f796fdde08",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

uri = rootPath + "/tmp/lancedb"
tableName = "user"
searchType = SearchType.hybrid
embedder = aliEmbedder

async def get_user_lancedb(user_id):
    # agno中的lancedb传table_name进去初始化时判断table是否存在只取了10条导致超出的表每次初始化都会当做不存在被重新创建, 所以表存在时自己打开好传进去避免被重新创建
    table_name = tableName + str(user_id)
    async_connection = await lancedb.connect_async(uri)
    table_names = await async_connection.table_names()
    table = await async_connection.open_table(tableName + str(user_id)) if table_name in table_names else None
    logger.info(f"init user lancedb: total={len(table_names)}, table_name={table_name}, exists=" + str(table is not None))
    
    db = LanceDb(
        uri=uri,
        table_name=table.name if table else table_name,
        table=table,
        async_connection=async_connection,
        search_type=searchType,
        embedder=embedder,
    )
    if table:
        schema = await table.schema()
        db._vector_col = schema.names[0]
        db._id = schema.names[1]  # type: ignore
    return db

async def vector_add(url, file_type, user_id, attachment_id, file_name, save_parse_file=False):
    """
    文档写入向量接口，接收文档URL，将文档内容写入向量数据库。
    """
    try:
        vector_db = await get_user_lancedb(user_id)
        chunk_size = 500
        max_chunk_size = 1500
        chunking_strategy = DocumentChunking(overlap=50 if file_type not in (
            'xlsx', 'xls', 'csv') else 0, chunk_size=chunk_size)
        fixed_chunking = FixedSizeChunking(chunk_size=chunk_size, overlap=50)

        content = await asyncio.to_thread(read_document_from_url, url, file_type)
        view_url = get_attachment_url(attachment_id, file_type=file_type)
        logger.info(f"解析文档成功(attachment_id={attachment_id}): {content}")
        documents = chunking_strategy.chunk(
            Document(content=content, meta_data={"url": view_url}))
        # 防止无段落或单个段落内容过长需要对长段落再做一次按字数分隔
        if file_type in ('xlsx', 'xls', 'csv'):
            # 表格要做拆分的话需要把数据行分成多块并把表头加上
            new_documents = []
            for doc in documents:
                # 取前5行作为表头
                rows = doc.content.split('\n')
                header = '\n'.join(rows[:5])
                # 数据行分块, 逐行读取
                data_rows = rows[5:]
                size = 0
                content = ''
                for row in data_rows:
                    size += len(row) + 1
                    content += row + '\n'
                    if size > max_chunk_size:
                        size = 0
                        new_documents.append(Document(content=header + '\n' + content, meta_data={"url": view_url}))
                        content = ''
                if content:
                    new_documents.append(Document(content=header + '\n' + content, meta_data={"url": view_url}))
            documents = new_documents

        else:
            list = [(fixed_chunking.chunk(doc) if len(doc.content) >
                    max_chunk_size else [doc]) for doc in documents]
            documents = [item for sublist in list for item in sublist]

            # 为每个文档块添加文件名作为标题
        for doc in documents:
            doc.content = f"文件名：{file_name}\n\n{doc.content}"

        logger.info(f"文档分割成功(attachment_id={attachment_id}): {documents}")
        knowledge_base = DocumentKnowledgeBase(
            documents=documents,
            vector_db=vector_db,
        )
        # if file_type in ['txt', 'md']: # 文本
        #     knowledge_base = UrlKnowledge(
        #         urls=[url],
        #         vector_db=vector_db,
        #         chunking_strategy=chunking_strategy,
        #     )
        # elif file_type == "pdf":
        #     knowledge_base = PDFUrlKnowledgeBase(
        #         urls=[url],
        #         vector_db=vector_db,
        #         chunking_strategy=chunking_strategy,
        #     )
        # elif file_type == 'csv':
        #     knowledge_base = CSVUrlKnowledgeBase(
        #         urls=[url],
        #         vector_db=vector_db,
        #         chunking_strategy=chunking_strategy,
        #     )
        # else:
        #    return {"status": "error", "message": "不支持的文件类型"}
        max_retries = 10
        min_delay = 3.0  # 最小浮动延迟时间(秒)
        max_delay = 10.0  # 最大浮动延迟时间(秒)
        for attempt in range(max_retries + 1):
            try:
                await knowledge_base.aload(upsert=True)
                break
            except RuntimeError as e:
                if "Commit conflict" in str(e) and attempt < max_retries:
                    wait_time = (attempt + 1) * (min_delay + (max_delay - min_delay) * random.random())  # 倍数增加的随机等待时间
                    logger.warning(f"文档写入向量库并发写入冲突(attachment_id={attachment_id})，第{attempt+1}次重试，等待{wait_time:.2f}秒...")
                    await asyncio.sleep(wait_time)
                    continue
                raise e
        doc_list = knowledge_base.document_lists
        doc_ids = []
        for docs in doc_list:
            for doc in docs:
                doc_ids.append(gen_vector_id(doc.content))
        logger.info(
            f"文档写入向量库成功(attachment_id={attachment_id}), 向量id: " + str(doc_ids))
        # 更新向量id到附件表
        async_session = get_async_mysql()
        async with async_session() as session:
            await session.execute(
                text(
                    "UPDATE `attachment` SET `vector_ids` = :vector_ids WHERE `id` = :id"),
                {'vector_ids': ",".join(doc_ids), 'id': attachment_id}
            )
            await session.commit()
            logger.info(f"更新向量id到附件表成功(attachment_id={attachment_id})")
        # 知识库原始文档写入本地
        if save_parse_file:
            if not await write_user_file(user_id=user_id, file_id=attachment_id, content=content, file_name=file_name, file_url=view_url):
                return {"status": "error", "message": f"文档写入本地文件失败(attachment_id={attachment_id})"}
        return {"status": "success", "message": "处理文档成功"}
        
    except Exception as e:
        msg = f"文档写入向量库失败(attachment_id={attachment_id}): "
        logger.error(msg + traceback.format_exc())
        return {"status": "error", "message": msg + str(e)}


async def vector_del(ids, user_id):
    """
    文档删除接口，接收文档向量ID列表，将对应的文档内容从向量数据库中删除。
    """
    try:
        vector_db = await get_user_lancedb(user_id)
        if not await vector_db.async_exists():  # 不存在则创建异步连接
            vector_db._get_async_connection()
        table = vector_db.async_table
        ids_str = "', '".join([str(v) for v in ids])

        # 添加并发冲突重试机制
        max_retries = 10
        min_delay = 3.0  # 最小浮动延迟时间(秒)
        max_delay = 10.0  # 最大浮动延迟时间(秒)

        for attempt in range(max_retries + 1):
            try:
                await table.delete(where=f"id in ('{ids_str}') ")
                logger.info(f"文档删除向量成功(ids={ids_str})")
                break
            except RuntimeError as e:
                if "Commit conflict" in str(e) and attempt < max_retries:
                    wait_time = (attempt + 1) * (min_delay + (max_delay - min_delay) * random.random())  # 倍数增加的随机等待时间
                    logger.warning(f"文档删除向量库并发删除冲突(ids={ids_str})，第{attempt+1}次重试，等待{wait_time:.2f}秒...")
                    await asyncio.sleep(wait_time)
                    continue
                raise e

    except Exception as e:
        logger.error(f"文档删除向量失败(ids={ids_str if 'ids_str' in locals() else ids}): {str(e)}")
        return {"status": "error", "message": "删除文档向量失败"}
    return {"status": "success", "message": "删除文档向量成功"}


def gen_vector_id(content: str):
    """
    通过文档内容生成向量ID(与agno中生成方式一致)，接收文档内容，返回对应的向量ID。
    """
    return md5(content.replace("\x00", "\ufffd").encode()).hexdigest()


async def vector_search(query, user_id):
    """
    文档检索接口，接收查询字符串，返回与查询相关的文档内容。
    """
    vector_db = await get_user_lancedb(user_id)
    logger.info("当前用户文档向量数: " + str(vector_db.get_count()))
    logger.info(vector_db.hybrid_search(query))  # agno封装的查询, 会自动创建不存在的全文索引
    # 原生查询, 可以看到向量id
    results = vector_db.table.search(
        vector_column_name='vector',
        query_type="hybrid",
    ).vector(aliEmbedder.get_embedding(query)).text(query).limit(3).to_pandas()
    for _, item in results.iterrows():
        payload = json.loads(item["payload"])
        logger.info(item)
        logger.info(payload)
        print("id=" + gen_vector_id(payload["content"]))
    agent = Agent(
        model=modelQwen,
        # Agentic RAG is enabled by default when `knowledge` is provided to the Agent.
        knowledge=AgentKnowledge(
            vector_db=vector_db,
        ),
        # search_knowledge=True gives the Agent the ability to search on demand
        # search_knowledge is True by default
        search_knowledge=True,
        instructions=[
            "Include sources url in your response.",
            "Always search your knowledge before answering the question.",
            "Only include the output in your response. No other text.",
        ],
        markdown=True,
    )
    agent.print_response(query, stream=True)


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    # loop.run_until_complete(vector_add("https://file.upfile.live/uploads/20250421/5b25ba5312f5d49c90a534be7cf92212.pdf", "pdf", "1", "1"))
    # loop.run_until_complete(vector_add("https://file.upfile.live/uploads/20250421/bda544442dd24eae839b08c5aa9528a7.doc", "doc", "1", "2"))
    # loop.run_until_complete(vector_add("https://file.upfile.live/uploads/20250421/9953d6ec59dc527eed2f9422f29082c4.txt", "txt", "1", "3"))
    # loop.run_until_complete(vector_add("https://agno-public.s3.amazonaws.com/csvs/employees.csv", "csv", "1", "4"))
    # loop.run_until_complete(vector_del(['c269079d7bfd8033652c4972bc557c25', '91b8829d95ae986b788707d092088a0b', '9d418336b08309a4d3d4762f2af3bc48', 'efc5b00da4227b270521d18475fe6345', 'b7a6516310ea79a03a780354a6fd6bf6', '9358fd51a59abe6d58333cdfdd1c46e6', '95203f17d86386b5f2e4c3187228894d', '09282d6d6fd905b40e57781faf52d600', '6de81f7d4e11be52223ff1fe886b8788', 'e4d3d5a7a02664dc0df667528f4b0769', 'c1c8c2e31617b893e1811650619cc50f'], "1"))
    loop.run_until_complete(vector_search("九江学院计算机科学与技术2017报考人数是多少", 1))

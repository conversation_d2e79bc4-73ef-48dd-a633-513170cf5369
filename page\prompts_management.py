import streamlit as st
import redis
from utils.redis import get_client
import time
import json
import pandas as pd
from utils.mysql import get_sync_mysql
from sqlalchemy import text
from utils.agent_prompt import MAIN_INSTRUCTIONS, USER_INSTRUCTIONS

# 提示词管理功能


def get_users_for_whitelist():
    """获取用户列表用于白名单选择"""
    try:
        db_session = get_sync_mysql()
        with db_session() as session:
            query = text("""
                SELECT
                    u.id,
                    u.name,
                    u.mobile
                FROM
                    user u
                WHERE
                    u.deleted_at IS NULL AND is_activated = 1
                ORDER BY
                    u.id ASC
                LIMIT 1000
            """)

            result = session.execute(query)
            users_data = []
            for row in result:
                # 格式化显示文本：ID - 姓名 (手机号)
                display_text = f"{row.id} - {row.name}"
                if row.mobile:
                    display_text += f" ({row.mobile})"

                users_data.append({
                    "id": row.id,
                    "name": row.name,
                    "mobile": row.mobile or "",
                    "display_text": display_text
                })

            return users_data
    except Exception as e:
        st.error(f"获取用户列表失败: {e}")
        return []


def render_prompts_management():
    st.header("提示词管理")

    # 尝试创建Redis连接
    try:
        redis_client = get_client()
    except Exception as e:
        redis_client = None
        st.error(f"Redis连接失败: {e}")

    # 定义回调函数
    def on_save():
        # 将表单数据存入会话状态
        st.session_state.form_content = st.session_state.content_input
        st.session_state.form_model = st.session_state.model_input
        st.session_state.form_whitelist = st.session_state.whitelist_input
        st.session_state.save_requested = True

    def on_reset():
        # 重置为默认提示词
        st.session_state.form_content = MAIN_INSTRUCTIONS
        st.session_state.reset_requested = True

    # 初始化提示词管理的会话状态变量
    if "prompts_initialized" not in st.session_state:
        st.session_state.prompts_initialized = True
        st.session_state.save_requested = False
        st.session_state.save_success = False
        st.session_state.save_error = None
        st.session_state.reset_requested = False
        st.session_state.reset_success = False

        # 从Redis获取初始内容
        if redis_client:
            try:
                st.session_state.form_content = redis_client.get(
                    "main_prompts") or ""
                st.session_state.form_model = redis_client.get(
                    "cur_main_model") or "claude-3-5-haiku-latest"

                # 获取白名单用户列表
                whitelist_data = redis_client.get("prompt_whitelist_users")
                if whitelist_data:
                    st.session_state.form_whitelist = json.loads(
                        whitelist_data)
                else:
                    st.session_state.form_whitelist = []  # 默认白名单为空
            except Exception as e:
                st.warning(f"获取已保存内容失败: {str(e)}")
                st.session_state.form_content = ""
                st.session_state.form_model = "claude-3-5-haiku-latest"
                st.session_state.form_whitelist = []
        else:
            st.session_state.form_content = ""
            st.session_state.form_model = "claude-3-5-haiku-latest"
            st.session_state.form_whitelist = []

    # 处理保存请求
    if st.session_state.get('save_requested', False):
        with st.spinner("正在保存..."):
            try:
                if redis_client:
                    # 保存到Redis
                    redis_client.set(
                        "main_prompts", st.session_state.form_content)
                    redis_client.set("cur_main_model",
                                     st.session_state.form_model)
                    # 保存白名单
                    redis_client.set("prompt_whitelist_users",
                                     json.dumps(st.session_state.form_whitelist))
                    st.session_state.save_success = True
                    st.session_state.save_error = None
                else:
                    st.session_state.save_success = False
                    st.session_state.save_error = "Redis连接不可用"
            except Exception as e:
                st.session_state.save_success = False
                st.session_state.save_error = str(e)
            finally:
                st.session_state.save_requested = False

    # 处理重置请求
    if st.session_state.get('reset_requested', False):
        st.session_state.reset_success = True
        st.session_state.reset_requested = False

    # 显示保存结果
    if st.session_state.get('save_success', False):
        st.success("内容已成功保存！")
        # 短暂延迟以确保消息显示
        time.sleep(0.1)
        st.session_state.save_success = False

    if st.session_state.get('save_error'):
        st.error(f"保存失败: {st.session_state.save_error}")
        st.session_state.save_error = None

    # 显示重置结果
    if st.session_state.get('reset_success', False):
        st.success("已重置为默认系统提示词！")
        time.sleep(0.1)
        st.session_state.reset_success = False

    # 获取所有用户
    all_users = get_users_for_whitelist()

    # 创建表单
    with st.form(key="prompts_form"):
        # 创建用户选项字典 {user_id: display_text}
        user_options = {user["id"]: user["display_text"] for user in all_users}

        # 获取当前白名单中的用户ID，只保留存在于选项中的用户
        current_whitelist = st.session_state.get('form_whitelist', [])
        valid_whitelist = [
            uid for uid in current_whitelist if uid in user_options.keys()]

        # 创建白名单多选框
        st.multiselect(
            "选择白名单用户",
            options=list(user_options.keys()),
            default=valid_whitelist,
            format_func=lambda x: user_options.get(x, f"用户ID: {x}"),
            key="whitelist_input",
            help="选择可以使用自定义提示词的用户"
        )

        # 创建模型选择下拉框
        model_options = ["claude-sonnet-4-20250514", "claude-3-7-sonnet-20250219",
                         "claude-3-5-haiku-latest", "gemini25pro", "gpt4.1", "kimi-k2"]
        default_index = 0
        if st.session_state.get('form_model') in model_options:
            default_index = model_options.index(st.session_state.form_model)

        st.selectbox(
            "选择模型",
            options=model_options,
            index=default_index,
            key="model_input",
            help="请选择要使用的模型"
        )

        # 创建文本编辑器
        current_content = st.session_state.get('form_content', "")

        # 显示提示信息
        if not current_content or not current_content.strip():
            st.info("💡 提示词为空时将使用默认系统提示词")

        st.text_area(
            "系统提示词",
            value=current_content,
            height=500,
            key="content_input",
            help="在这里输入或编辑文本内容，留空将使用默认系统提示词"
        )

        # 按钮行
        col1, col2 = st.columns([1, 1])
        with col1:
            st.form_submit_button(
                label="💾 保存内容", on_click=on_save, type="primary")
        with col2:
            st.form_submit_button(
                label="🔄 重置为默认", on_click=on_reset, type="secondary")

    # 默认提示词查看
    st.subheader("默认系统提示词")
    with st.expander("查看默认系统提示词内容", expanded=False):
        st.code(MAIN_INSTRUCTIONS, language="text")
        st.caption("这是系统默认的提示词内容，当自定义提示词为空时将使用此内容")

from agno.tools import Toolkit, tool
from typing import Any
import json
import requests
from utils.context import get_context
from utils.logger import logger
import os
from dotenv import load_dotenv
from utils.pyapi import call_api
load_dotenv()

class TodoistTools(Toolkit):
    """用于管理待办事项的工具集合"""

    def __init__(self):
        super().__init__(name="todoist_tools")  # 指定工具包名称

        # 根据文档要求，必须手动注册所有工具函数
        self.register(self.add_todo)
        self.register(self.update_todo)
        self.register(self.update_todo_date)
        self.register(self.delete_todo)
        self.register(self.get_todo)
        self.register(self.complete_todo)
        self.register(self.get_active_todos)
        self.register(self.get_completed_todos)
        self.register(self.todo_bind_cus_busi)
        self.register(self.sort_todo)
        logger.info(f"TodoistTools工具注册完成, 实例ID: {id(self)}")

    def add_todo(self, title: str, start_time: str = None, due_date: str = None, notify_time: str = "", notify_cycle: int = 0, priority: str = "", customer_id: int = 0, bo_id: int = 0, remark: str = "", type: int = 1, is_finished: int = 0) -> str:
        """
        添加一个新的待办事项

        Args:
            title: 待办事项标题，（不用带上时间点）
            start_time: 开始时间，字符串格式："Y-M-D H:M:S"，非必填，开始时间要和截止时间一起传
            due_date: 截止日期，字符串格式："Y-M-D H:M:S"，（非必填，截止时间到了后不会再发提醒或执行AI待办任务，如果没有截止日期或无法判断截止日期则不需要填，如果notify_cycle有值则默认不需要截止日期）
            notify_time: 提醒时间或AI待办任务执行时间，字符串格式："Y-M-D H:M:S", 非必填，如果start_time或due_date字段有值，但是没有明确说明notify_time的值，需要根据待办内容，选择一个合理的提醒时间，循环周期以该日期为基准计算第一个周期(当天的时间还没过的话则包括当天)提醒时间，例如想要每周三提醒则该日期需为周三
            notify_cycle: 通知或AI待办任务执行循环周期：0 永不（默认），1 每天，2 工作日，3 周末，4 每周，5 每两周，6 每月，7 每三个月，8 每六个月，9 每年 
            priority: 优先级(低，中，高)
            customer_id: 客户ID,和客户相关的待办事项
            bo_id: 商机id,和某个商机相关的待办事项
            reamark: 待办备注
            type: 待办类型: 1 普通待办(当待办是需要用户自己去做的事的时候选这个类型)，2 AI待办(由AI去执行的待办, 当待办是用户要求帮他做的事或基于当前系统能力能够帮用户做的事的时候选这个类型)
            is_finished: 是否完成: 0 未完成，1 已完成
        Returns:
            str: 包含操作结果的JSON字符串
        """
        try:
            if start_time and not due_date: due_date = start_time
            if due_date and not start_time: start_time = due_date
            if start_time and not notify_time: notify_time = start_time
            data: dict[str, Any] = {
                "title": title,
                # 循环通知默认不填截止日期(提示词不生效)
                "start_time": None if notify_cycle else start_time,
                "due_date": None if notify_cycle else due_date,
                "notify_time": notify_time,
                "notify_cycle": notify_cycle,
                "priority": priority,
                "user_id": get_context("user_id"),
                "customer_id": customer_id,
                "bo_id": bo_id,
                "description": remark,
                "type": type,
                "is_finished": is_finished
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/todo/add", data=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"添加待办失败:{str(response.text)}")
                return json.dumps({"error": "添加待办失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"添加待办失败: {str(e)}")
            logger.error(f"添加待办失败:{str(response.text)}")
            result = {
                "success": False,
                "message": f"添加待办失败"
            }
            return json.dumps(result, ensure_ascii=False)

    def update_todo(self, todo_id: int,  updates: dict) -> str:
        """
        更新现有的待办事项

        Args:
            todo_id: 待办事项ID
            updates: 待更新的数据，支持以下字段(仅传需要更新的字段)：
                title: 待办事项标题
                priority: 优先级(10为低，20为中，30为高)
                customer_id: 客户ID
                bo_id: 商机ID
                description: 待办备注
                type: 待办类型: 1 普通待办，2 AI待办
                is_finished: 是否完成: 0 未完成，1 已完成
        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"尝试更新待办事项 ID: {todo_id}, 更新内容: {updates}")
        try:
            data: dict[str, Any] = {
                "id": todo_id,
                "user_id": get_context("user_id"),
                **updates
            }
            response = call_api("/pyapi/todo/edit", data)
            return response
        except Exception as e:
            logger.error(f"更新待办事项失败: {str(e)}")
            result = {
                "success": False,
                "message": f"更新待办事项失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)
    
    def update_todo_date(self, todo_id: int,  updates: dict) -> str:
        """
        更新现有的待办事项时间

        Args:
            todo_id: 待办事项ID
            updates: 待更新的数据，支持以下字段：
                start_time: 开始时间，格式为YYYY-MM-DD HH:MM:SS（非必填，要清空时传None）
                due_date: 截止日期，格式为YYYY-MM-DD HH:MM:SS（非必填，要清空时传None）
                notify_time: 提醒时间，格式为YYYY-MM-DD HH:MM:SS （非必填，要清空时传None，如果due_date字段有值，notify_time没值的话，需要根据待办内容，选择一个合理的提醒时间）
                notify_cycle: 非必填，通知循环周期：0 无，1 每天，2 工作日，3 周末，4 每周，5 每两周，6 每月，7 每三个月，8 每半年，9 每年 
        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"尝试更新待办事项 ID: {todo_id}, 更新内容: {updates}")
        try:
            data: dict[str, Any] = {
                "id": todo_id,
                # "due_date": due_date,
                # "notify_time": notify_time,
                # "notify_cycle": notify_cycle,
                "user_id": get_context("user_id"),
                **updates
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/todo/updDate", json=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"更新待办事项时间失败: {str(response.text)}")
                return json.dumps({"error": "更新待办事项时间失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"更新待办事项时间失败: {str(e)}")
            result = {
                "success": False,
                "message": f"更新待办事项时间失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    def todo_bind_cus_busi(self, todo_id: int,  customer_id: int = 0, bo_id: int = 0) -> str:
        """
        将待办信息关联客户信息,商机信息，支持只关联一个或都关联

        Args:
            todo_id: 待办事项ID
            customer_id: 客户ID
            bo_id: 商机ID

        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"todo_bind_cus_busi ID: {todo_id}")
        try:
            data: dict[str, Any] = {
                "todo_id": todo_id,
                "customer_id": customer_id,
                "bo_id": bo_id,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/todo/bindCusBusi", data=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"待办关联客户商机失败: {str(response.text)}")
                return json.dumps({"error": "待办关联客户商机失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"待办关联客户商机失败: {str(e)}")
            result = {
                "success": False,
                "message": f"待办关联客户商机失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    def delete_todo(self, todo_id: int | str) -> str:
        """
        删除待办事项, 支持批量删除

        Args:
            todo_id: 待办事项ID，多个id时用,间隔

        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"尝试删除待办事项 ID: {todo_id}")
        try:
            data: dict[str, Any] = {
                "id": todo_id,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/todo/del", data=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"删除待办事项失败: {str(response.text)}")
                return json.dumps({"error": "删除待办失败."})
        except Exception as e:
            logger.error(f"删除待办事项失败: {str(e)}")
            result = {
                "success": False,
                "message": f"删除待办事项失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    def sort_todo(self, todo_ids: str) -> str:
        """
        更新待办事项的排序顺序, 如果用户没有提供排序规则, 则按照下面规则排序

        # 通用排序提示词
        对待办数据进行智能排序，优先级从高到低：

        ## 排序规则
        1. 优先级标记为“高”或含“紧急”“重要”关键词的记录置顶。
        2. 待办关联商机或客户的记录，优先级显著提升。
        3. 截止时间/提醒时间接近当前（48小时内）的待办，优先级提升。
        4. 创建日期较新的记录，优先级略高。

        ## 输出
        按优先级从高到低排序的记录列表，附带简要排序理由。（不要展示todo_id)

        Args:
            todo_ids:  按优先级从高到低排好的待办事项ID列表(优先级高的在前面)，每个id之间用,间隔

        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"排序待办事项 ID: {todo_ids}")
        try:
            if not todo_ids:
                return json.dumps({
                    "status": "error",
                    "message": "todo_ids不能为空"
                }, ensure_ascii=False)
            data: dict[str, Any] = {
                "ids": todo_ids.replace(" ", "").split(","),  # 去除空格并分割,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/todo/sortAll", json=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"排序待办事项失败: {str(response.text)}")
                return json.dumps({"error": "排序待办失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"排序待办事项失败: {str(e)}")
            result = {
                "success": False,
                "message": f"排序待办事项失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    def get_todo(self, todo_id: int) -> str:
        """
        获取单个待办事项的详细信息

        Args:
            todo_id: 待办事项ID

        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"尝试获取待办事项 ID: {todo_id}")
        try:
            user_id = get_context("user_id")
            response = requests.get(
                f"{os.getenv('API_URL')}/pyapi/todo/detail?id={todo_id}&user_id={user_id}")
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"获取待办事项详细信息失败: {str(response.text)}")
                return json.dumps({"error": "获取待办事项详细信息失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"获取待办事项详细信息失败: {str(e)}")
            result = {
                "success": False,
                "message": f"获取待办事项详细信息失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    def complete_todo(self, todo_id: int) -> str:
        """
        将待办事项标记为已完成

        Args:
            todo_id: 待办事项ID

        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"尝试将待办事项 ID: {todo_id} 标记为已完成")
        # 注意这里我们需要先获取update_todo的返回值（字典），然后转换为JSON字符串
        try:
            data: dict[str, Any] = {
                "id": todo_id,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/todo/complete", data=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"更新待办事项时间失败: {str(response.text)}")
                return json.dumps({"error": "更新待办事项状态失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"更新待办事项状态失败: {str(e)}")
            result = {
                "success": False,
                "message": f"更新待办事项状态失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    def get_active_todos(self, is_all: int = 1, start_date: str = "", end_date: str = "", bo_id: int = 0, customer_id: int = 0) -> str:
        """ Get  active (not completed) tasks. 用户没有明确提到时间时，不用传时间参数）

        Args:
           is_all (int): 是否获取所有待办，1表示是，0表示否(查询指定范围的待办)
           start_date (str): (可选)开始日期(Y-m-d),is_all为0时必传
           end_date (str): (可选)结束日期(Y-m-d),is_all为0时必传
           bo_id (int): (可选)商机id, 筛选指定商机下的待办
           customer_id (int): (可选)客户id, 筛选指定客户下的待办

        Returns:
             str: 包含所有未完成的任务列表（含已过期）的 JSON 字符串.

        """

        if is_all == 1:
            start_date = ""
            end_date = ""
        data: dict[str, Any] = {
            "user_id": get_context("user_id"),
            "start_date": start_date,
            "end_date": end_date,
            "bo_id": bo_id,
            "customer_id": customer_id,
        }
        response = requests.post(
            f"{os.getenv('API_URL')}/pyapi/todo/actives", data=data)
        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(response.text)
            return json.dumps({"error": "Failed to fetch active tasks."}, ensure_ascii=False)

    def get_completed_todos(self) -> str:
        """ Get all completed tasks.

           Returns:
             str: 包含所有已完成的任务列表的JSON 字符串 和链接地址.

        """
        user_id = get_context("user_id")

        response = requests.post(
            f"{os.getenv('API_URL')}/pyapi/todo/completed?user_id={user_id}")
        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(response.text)
            return json.dumps({"error": "Failed to fetch completed tasks."}, ensure_ascii=False)

import streamlit as st
import subprocess
import time

# 简易部署代码
# 启动服务  streamlit run deploy.py --server.port 8502

# 定义固定的令牌值
VALID_TOKEN = "2G9iQ4xq8uVqJbWnhUh5YTKHBIKOGHJHKBGJJMHDLGHqUWiRDBNBVWATBJKL"

# 获取URL参数
token = st.query_params.get("token", "")

# 验证令牌
if token != VALID_TOKEN:
    st.error("404 not found")
    st.stop()


def run_command(command, working_directory=None):
    try:
        result = subprocess.run(
            command,
            cwd=working_directory,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error: {e.stderr}"


# 添加自定义CSS来增加标签字体大小和间距
st.markdown("""
<style>
    .stTabs [data-baseweb="tab-list"] {
        gap: 24px;
    }
    .stTabs [data-baseweb="tab"] {
        font-size: 30px;
        font-weight: bold;
        padding: 10px 20px;
    }
</style>
""", unsafe_allow_html=True)

st.title("部署代码")

# 创建两个选项卡：前端和后端
tab1, tab2, tab3 = st.tabs(["前端部署", "后端部署", "后端部署-生产环境"])

# 前端选项卡
with tab1:

    if st.button("测试环境-前端代码"):
        with st.spinner("部署前端代码..."):
            frontend_dir = "/var/www/html/dnmp/www/test/sale-agent-web"

            # Execute git pull in the frontend directory
            success, output = run_command(f"cd {frontend_dir} && git pull")

            if success:
                st.success("前端更新成功!")
                st.code(output)
            else:
                st.error("前端更新失败")
                st.code(output)

# 后端选项卡
with tab2:

    if st.button("测试环境-sale-agent-api"):
        with st.spinner("部署后端代码..."):
            backend_dir = "/var/www/html/dnmp/www/test/sale-agent-api"
            docker_dir = "/www/test/sale-agent-api"

            # Execute git pull in the backend directory
            # success, output = run_command(f"cd {backend_dir} && git pull && docker exec -it  sale-test2  sh && cd {docker_dir} && php start.php reload")
            success, output = run_command(
                f"cd {backend_dir} && git pull"
            )

            if success:
                st.success("后端代码更新成功!")
                st.code(output)

                # 使用非交互式命令执行docker命令
                st.write("正在重启后端服务...")
                reload_success, reload_output = run_command(
                    f"docker exec -i sale-test2 /bin/sh -c 'cd {docker_dir} && php start.php reload'"
                )

                if reload_success:
                    st.success("后端服务重启成功!")
                    st.code(reload_output)
                else:
                    st.error("后端服务重启失败")
                    st.code(reload_output)
            else:
                st.error("后端更新失败")
                st.code(output)
    
    st.markdown("<br>", unsafe_allow_html=True)  # 添加空行分隔按钮
    
    if st.button("测试环境-python-agent"):
        with st.spinner("部署Python Agent服务..."):
            python_agent_dir = "/var/www/html/dnmp/www/test/sale-agent"
            
            # 1. 更新代码
            pull_success, pull_output = run_command(f"cd {python_agent_dir} && git pull")
            
            if pull_success:
                st.success("Python Agent代码更新成功!")
                st.code(pull_output)
                
                # 2. 查找进程ID并重启服务
                st.write("正在重启Python Agent服务...")
                
                # 查找主进程ID并重启
                find_pid_cmd = "ps -ef | grep \"/www/test/sale-agent/.venv/bin/gunicorn\" | awk '$3 == 1 {print $2}'"
                find_success, pid_output = run_command(find_pid_cmd)
                
                if find_success and pid_output.strip():
                    pid = pid_output.strip()
                    # 发送HUP信号重启服务
                    restart_success, restart_output = run_command(f"kill -HUP {pid}")
                    
                    time.sleep(2)  # 等待2秒
                    # 检查 main:app 服务是否还在运行
                    check_cmd = 'ps -ef | grep "/www/test/sale-agent/.venv/bin/gunicorn" | grep -v grep'
                    check_success, check_output = run_command(check_cmd)
                    
                    if restart_success:
                        if check_success and check_output.strip():
                            st.success(f"Python Agent服务重启成功! 进程ID: {pid}，服务正常运行。")
                        else:
                            st.warning(f"服务重启命令已执行，但未检测到 main:app 进程，请手动检查。\n{check_output}")
                    else:
                        st.error("Python Agent服务重启失败")
                        st.code(restart_output)
                else:
                    st.error("无法找到Python Agent服务进程ID")
                    st.code(pid_output)
            else:
                st.error("Python Agent代码更新失败")
                st.code(pull_output)

with tab3:

    if st.button("生产环境-sale-agent-api"):
        with st.spinner("部署后端代码..."):
            backend_dir = "/var/www/html/dnmp/www/prod/sale-agent-api"
            docker_dir = "/www/sale-agent-api"

            # Execute git pull in the backend directory
            # success, output = run_command(f"cd {backend_dir} && git pull && docker exec -it  sale-test2  sh && cd {docker_dir} && php start.php reload")
            success, output = run_command(
                f"cd {backend_dir} && git pull"
            )

            if success:
                st.success("后端代码更新成功!")
                st.code(output)

                # 使用非交互式命令执行docker命令
                st.write("正在重启后端服务...")
                reload_success, reload_output = run_command(
                    f"docker exec -i prod-php83 /bin/sh -c 'cd {docker_dir} && php start.php reload'"
                )

                if reload_success:
                    st.success("后端服务重启成功!")
                    st.code(reload_output)
                else:
                    st.error("后端服务重启失败")
                    st.code(reload_output)
            else:
                st.error("后端更新失败")
                st.code(output)
    
    st.markdown("<br>", unsafe_allow_html=True)  # 添加空行分隔按钮
    
    if st.button("生产环境-python-agent"):
        with st.spinner("部署Python Agent服务..."):
            python_agent_dir = "/var/www/html/dnmp/www/prod/sale-agent"
            
            # 1. 更新代码
            pull_success, pull_output = run_command(f"cd {python_agent_dir} && git pull")
            
            if pull_success:
                st.success("Python Agent代码更新成功!")
                st.code(pull_output)
                
                # 2. 查找进程ID并重启服务
                st.write("正在重启Python Agent服务...")
                
                # 查找主进程ID并重启
                find_pid_cmd = "ps -ef | grep \"/www/prod/sale-agent/.venv/bin/gunicorn\" | awk '$3 == 1 {print $2}'"
                find_success, pid_output = run_command(find_pid_cmd)
                
                if find_success and pid_output.strip():
                    pid = pid_output.strip()
                    # 发送HUP信号重启服务
                    restart_success, restart_output = run_command(f"kill -HUP {pid}")
                    
                    time.sleep(2)  # 等待2秒
                    # 检查 main:app 服务是否还在运行
                    check_cmd = 'ps -ef | grep "/www/prod/sale-agent/.venv/bin/gunicorn" | grep -v grep'
                    check_success, check_output = run_command(check_cmd)
                    
                    if restart_success:
                        if check_success and check_output.strip():
                            st.success(f"Python Agent服务重启成功! 进程ID: {pid}，服务正常运行。")
                        else:
                            st.warning(f"服务重启命令已执行，但未检测到 main:app 进程，请手动检查。\n{check_output}")
                    else:
                        st.error("Python Agent服务重启失败")
                        st.code(restart_output)
                else:
                    st.error("无法找到Python Agent服务进程ID")
                    st.code(pid_output)
            else:
                st.error("Python Agent代码更新失败")
                st.code(pull_output)
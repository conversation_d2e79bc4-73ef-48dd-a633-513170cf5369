import asyncio
import os
from pathlib import Path
import sys


# 添加项目根目录到Python路径
rootPath = str(Path(__file__).parent.parent.parent.parent)
sys.path.append(rootPath)

from utils.model_providers.anthropic.extended_claude import ExtendedClaude
from agno.models.base import Message


async def example_usage():
    # 创建扩展的Claude实例
    os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
    claude = ExtendedClaude(
        id="claude-3-5-sonnet-20241022",  # 或其他Claude模型ID
    )

    # 创建消息
    messages = [
        Message(role="user", content="Hello, how are you?")
    ]

    try:
        # 使用扩展的Claude实例发送消息
        response = claude.invoke(messages)
        print("Response:", response)
        # 使用扩展的Claude实例异步发送消息
        async_response = await claude.ainvoke(messages)
        print("Async Response:", async_response)

    except Exception as e:
        print(f"Error occurred: {e}")


if __name__ == "__main__":
    asyncio.run(example_usage())

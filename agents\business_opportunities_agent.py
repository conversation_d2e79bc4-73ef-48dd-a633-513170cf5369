from agno.agent import Agent
from agno.models.openai.like import OpenAILike
from tools.business_opportunities_tools import BusinessOpportunitiesTools
from utils.storage import get_singlestore_storage
from utils.models import mainModel
import copy

# 获取SingleStore存储实例
storage = get_singlestore_storage(table_name="sales_lead_sessions")


# 创建销售商机管理智能体
business_opportunities_agent = Agent(
    name="销售商机助手",
    agent_id="003",
    model=mainModel,
    reasoning=True,
    markdown=False,
    debug_mode=True,
    show_tool_calls=True,
    tools=[copy.deepcopy(BusinessOpportunitiesTools())],
    # storage=storage,
    instructions="""
你是一个专业的销售商机管理助手，负责帮助用户管理和跟进销售商机。

核心职责：
- 添加、更新商机（名称、预算、状态等）
- 查询商机详情或列表
- 推荐商机推进策略
- 确保商机与客户关联

可用工具：
- add_lead：添加新的销售商机,添加之前判断是否已经存在相似的商机（调用list_leads）,已经存在则不添加
- update_lead：更新商机信息（预算，来源，状态）
- bind_customer：将商机与客户关联
- get_lead：查询某个商机详细信息
- list_leads：列出用户的所有商机
- delete_lead：删除不需要的商机创建新的待办任务

规则与注意事项：
- 如果没有商机id,可以调用list_leads获取商机id
- 如果工具执行结果有link链接地址，需要返回输出链接地址，方便让用户可以点击查看链接
"""
)

from typing import Optional
from agno.vectordb.lancedb import LanceDb as BaseLanceDb, SearchType
import lancedb
from agno.embedder import Embedder
from agno.reranker.base import Reranker
from agno.vectordb.distance import Distance

class LanceDb(BaseLanceDb):
    def __init__(
        self,
        uri: lancedb.URI = "/tmp/lancedb",
        connection: Optional[lancedb.LanceDBConnection] = None,
        table: Optional[lancedb.db.LanceTable] = None,
        async_connection: Optional[lancedb.AsyncConnection] = None,
        async_table: Optional[lancedb.db.AsyncTable] = None,
        table_name: Optional[str] = None,
        api_key: Optional[str] = None,
        embedder: Optional[Embedder] = None,
        search_type: SearchType = SearchType.vector,
        distance: Distance = Distance.cosine,
        nprobes: Optional[int] = None,
        reranker: Optional[Reranker] = None,
        use_tantivy: bool = True,
        on_bad_vectors: Optional[str] = None,  # One of "error", "drop", "fill", "null".
        fill_value: Optional[float] = None,  # Only used if on_bad_vectors is "fill"
    ):
        self.table_name = table_name # 超出10个的表被误判不存在导致table_name没有赋值, 这里提前赋值一下
        super().__init__(uri, connection, table, async_connection, async_table, table_name, api_key, embedder, search_type, distance, nprobes, reranker, use_tantivy, on_bad_vectors, fill_value)
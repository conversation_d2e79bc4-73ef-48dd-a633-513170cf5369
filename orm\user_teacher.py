from typing import List, Optional
from sqlalchemy import Column, Integer, String, DateTime, and_, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from orm.base_orm import FormattedDateTime
from utils.mysql import get_sync_mysql

Base = declarative_base()

class UserTeacher(Base):
    """用户老师关联模型"""
    __tablename__ = 'user_teacher'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, default=0, index=True)
    teacher_id = Column(Integer, nullable=False, index=True)
    invite_code = Column(String(50), nullable=False, default='')
    created_at = Column(FormattedDateTime, default=datetime.now)
    updated_at = Column(FormattedDateTime, default=datetime.now, onupdate=datetime.now)
    deleted_at = Column(FormattedDateTime, nullable=True, default=None)

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'teacher_id': self.teacher_id,
            'invite_code': self.invite_code,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'deleted_at': self.deleted_at
        }

def create_user_teacher(user_id: int, teacher_id: int, invite_code: str) -> Optional[UserTeacher]:
    """创建用户老师关联"""
    session = get_sync_mysql()
    with session() as session:
        try:
            user_teacher = UserTeacher(
                user_id=user_id,
                teacher_id=teacher_id,
                invite_code=invite_code
            )
            session.add(user_teacher)
            session.commit()
            session.refresh(user_teacher)
            return user_teacher
        except Exception as e:
            session.rollback()
            raise e

def update_user_teacher(relation_id: int, user_id: int = None, invite_code: str = None) -> Optional[UserTeacher]:
    """更新用户老师关联"""
    session = get_sync_mysql()
    with session() as session:
        try:
            relation = session.query(UserTeacher).filter(
                and_(
                    UserTeacher.id == relation_id,
                    UserTeacher.deleted_at.is_(None)
                )
            ).first()
            
            if not relation:
                return None
                
            if user_id is not None:
                relation.user_id = user_id
            if invite_code is not None:
                relation.invite_code = invite_code
            relation.updated_at = datetime.now()
            
            session.commit()
            session.refresh(relation)
            return relation
        except Exception as e:
            session.rollback()
            raise e

def delete_user_teacher(relation_id: int) -> bool:
    """软删除用户老师关联"""
    session = get_sync_mysql()
    with session() as session:
        try:
            relation = session.query(UserTeacher).filter(
                and_(
                    UserTeacher.id == relation_id,
                    UserTeacher.deleted_at.is_(None)
                )
            ).first()
            
            if not relation:
                return False
                
            relation.deleted_at = datetime.now()
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            raise e

def get_user_teachers_by_teacher_id(teacher_id: int) -> List[UserTeacher]:
    """根据老师ID获取所有关联的用户"""
    session = get_sync_mysql()
    with session() as session:
        return session.query(UserTeacher).filter(
            and_(
                UserTeacher.teacher_id == teacher_id,
                UserTeacher.deleted_at.is_(None)
            )
        ).order_by(UserTeacher.created_at.desc()).all()

def get_user_teachers_by_user_id(user_id: int) -> List[UserTeacher]:
    """根据用户ID获取所有关联的老师"""
    session = get_sync_mysql()
    with session() as session:
        return session.query(UserTeacher).filter(
            and_(
                UserTeacher.user_id == user_id,
                UserTeacher.deleted_at.is_(None)
            )
        ).order_by(UserTeacher.created_at.desc()).all()

def get_user_teacher_by_invite_code(invite_code: str) -> Optional[UserTeacher]:
    """根据邀请码获取关联关系"""
    session = get_sync_mysql()
    with session() as session:
        return session.query(UserTeacher).filter(
            and_(
                UserTeacher.invite_code == invite_code,
                UserTeacher.deleted_at.is_(None)
            )
        ).first()

def search_user_teachers(teacher_id: int = None, user_id: int = None, invite_code: str = None) -> List[UserTeacher]:
    """搜索用户老师关联"""
    session = get_sync_mysql()
    with session() as session:
        query = session.query(UserTeacher).filter(
            UserTeacher.deleted_at.is_(None)
        )
        
        if teacher_id:
            query = query.filter(UserTeacher.teacher_id == teacher_id)
        if user_id:
            query = query.filter(UserTeacher.user_id == user_id)
        if invite_code:
            query = query.filter(UserTeacher.invite_code.like(f'%{invite_code}%'))
            
        return query.order_by(UserTeacher.created_at.desc()).all()

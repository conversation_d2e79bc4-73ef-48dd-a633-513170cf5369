from agno.storage.singlestore import SingleStoreStorage
from sqlalchemy.engine import create_engine

def get_singlestore_storage(table_name="agent_sessions"):
    """
    创建并返回一个SingleStore存储实例
    
    参数:
        table_name: 存储会话的表名，默认为"agent_sessions"
        
    返回:
        SingleStoreStorage实例
    """
    USERNAME = "root"
    PASSWORD = "123456"
    HOST = "127.0.0.1"
    PORT = "3306"
    DATABASE = "saleagent"
    
    # SingleStore DB URL
    db_url = f"mysql+pymysql://{USERNAME}:{PASSWORD}@{HOST}:{PORT}/{DATABASE}?charset=utf8mb4"
    db_engine = create_engine(db_url)
    
    return SingleStoreStorage(
        # 存储会话的表名
        table_name=table_name,
        # db_engine: Singlestore数据库引擎
        db_engine=db_engine,
        # schema: Singlestore模式
        schema=DATABASE,
    ) 
from utils.logger import logger
from utils.context import get_context
from typing import Any
import requests
import json
from utils.pyapi import call_api
import os
from dotenv import load_dotenv
load_dotenv()


def ai_ppt(subject: str, content: str) -> str:
    """
    ai生成ppt

    Args:
        subject: ppt主题
        content: ppt内容

    Returns:
        str: ppt的预览地址，可以在线编辑和替换模板，并导出ppt文件
    """
    data: dict[str, Any] = {
        "user_id": get_context("user_id"),
        "subject": subject,
        "content": content,
    }

    return call_api("/pyapi/aippt/createV2", data)


def upd_ppt_temp(pptId: str, templateId: str, style: str = "", themeColor: str = "") -> str:
    """
    更换ai生成的ppt的模板

    Args:
        pptId: pptId
        templateId: 当前的模板Id
        style: 风格: 扁平简约/商务科技/文艺清新/卡通手绘/中国风/创意时尚/创意趣味, 只能从提供的模板中选择一个或为空
        themeColor: 主题色(橙色, 蓝色，紫色，青色，绿色，黄色，红色，棕色，白色，黑色)

    Returns:
        str: 包含ppt的下载地址的JSON字符串
    """

    try:

        data: dict[str, Any] = {
            "user_id": get_context("user_id"),
            "ppt_id": pptId,
            "neq_ids": templateId,
            "style": style,
            "theme_color": themeColor
        }
        endpoint = f"{os.getenv('API_URL')}/pyapi/aippt/updTemplate"
        response = requests.post(endpoint, json=data)
        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(f"ai更换ppt模板失败: {str(response.text)}")
            return json.dumps({"error": "ai更换ppt模板失败."}, ensure_ascii=False)
    except Exception as e:
        logger.error(f"ai更换ppt模板失败: {str(e)}")
        result = {
            "success": False,
            "message": f"ai更换ppt模板失败: {str(e)}"
        }
        return json.dumps(result, ensure_ascii=False)

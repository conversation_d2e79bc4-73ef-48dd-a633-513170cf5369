
from markitdown import MarkItDown

from utils.doc_parse import read_document_from_url


def convert_to_markdown(fileUrl: str, fileName: str, file_type: str) -> str:
    """
    用于解析文件/图片的内容(支持文档、音频、图片、截图等文件)
    Args:
        fileUrl (str): 文件的URL
        fileName (str): 文件名（带后缀）
        file_type (str): 文件的类型(后缀名)
    Returns:
        str: 解析后的内容    
    """
    try:
        return read_document_from_url(fileUrl, file_type)
    except Exception as e:
        return str({"status": "error", "message": f"解析文件失败: {str(e)}"})


def webpage_content_parse(url: str) -> str:
    """
    用于解析网页的内容
    Args:
        url (str): 网页URL
    Returns:
        str: 解析后的内容    
    """
    try:
        return read_document_from_url(url, "html")
    except Exception as e:
        return str({"status": "error", "message": f"解析网页失败: {str(e)}"})

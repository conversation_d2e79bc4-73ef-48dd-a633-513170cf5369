from utils.logger import logger
from utils.context import get_context
from typing import Any, List, Dict, Union
import requests
import json
import os
from dotenv import load_dotenv
load_dotenv()

def get_link(params: Union[List[Dict[str, Any]], Dict[str, Any]]) -> str:
    """
    批量获取链接地址: '待办列表/待办详情/客户列表/客户详情/商机列表/商机详情/销售漏斗/存储文件列表' 的链接地址

    Args:
        params: 可以是单个请求字典或请求字典列表
               单个请求格式: {"type": int, "third_id": str}
               - type: 链接类型：1,待办列表;2,待办详情;3,客户列表;4,客户详情;5,商机列表;6,商机详情;7,销售漏斗;8,存储文件列表/销售物料文件列表/最近上传文件列表
               - third_id: 待办id/客户id/商机id，可为空字符串

    Returns:
        str: 包含link链接地址的JSON字符串
    """

    try:
        # 如果传入的是单个字典，转换为列表
        if isinstance(params, dict):
            params_list = [params]
        else:
            params_list = params

        data: dict[str, Any] = {
            "user_id": get_context("user_id"),
            "params": params_list
        }
    
        endpoint = f"{os.getenv('API_URL')}/pyapi/link/getLinks"
        
        response = requests.post(endpoint, json=data)

        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(f"批量获取连接地址失败: {str(response.text)}")
            return json.dumps({"error": "批量获取连接地址失败."}, ensure_ascii=False)
    except Exception as e:
        logger.error(f"批量获取连接地址失败: {str(e)}")
        result = {
            "success": False,
            "message": f"批量获取连接地址失败: {str(e)}"
        }
        return json.dumps(result, ensure_ascii=False)

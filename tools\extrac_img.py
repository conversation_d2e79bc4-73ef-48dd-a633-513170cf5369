from pathlib import Path

from agno.agent import Agent
from agno.media import Image
from agno.models.openai.like import OpenAILike

modelQwen = OpenAILike(
    id="qwen-vl-max-latest",
    api_key="sk-1900eb026aee4b95992186f796fdde08",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    top_p=0.8,
    temperature=0.7,
)

agent = Agent(
    model=modelQwen,
    tools=[],
    markdown=True,
    debug_mode=True,
)


def extract_image_content(imgUrl: str) -> str:
    """ 用于解析图片的内容 
        Args:
        imgUrl: 图片的url，注意必须要传完整的图片url地址，有签名信息(signature)
    """
    response = agent.run(
        "用markdown格式输出图片的所有内容，不要漏掉任何内容，不用作任何的解释和说明.",
        images=[
            Image(url=imgUrl),
        ],
        stream=False,
    )
    return response.content

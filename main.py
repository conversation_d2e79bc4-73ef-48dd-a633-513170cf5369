from contextlib import asynccontextmanager
from http import HTTPStatus
import traceback
import logging
from typing import Any, Callable, Dict
from dotenv import load_dotenv
from agno.agent import Agent
from sqlalchemy import text
from tools.Knowledge_tools import KnowledgeTools
from tools.attachment_tools import AttachmentTools
from tools.chart_tools import generate_mermaid_chart
from tools.chat_tools import ChatTools
from tools.content_tools import send_message_to_user, small_content_writer
from tools.location_tools import LocationTools
from utils.context import get_context, set_context
from tools.enterprise_tools import EnterpriseTools
from utils.diskcache import cache_cleaner
from utils.knowledge.user_file_storage import delete_user_file, write_user_file, write_user_file_by_url
from utils.mysql import get_async_mysql
from utils.storage import get_singlestore_storage, num_history_runs, bs_num_history_runs
from fastapi import FastAPI, Query, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
import json
import time
import os
import asyncio
from copy import deepcopy
from agno.playground import serve_playground_app
from tools.extrac_img import extract_image_content
from utils.logger import logger, configure_agno_logging
from tools.reasoning import ReasoningTools
# from agno.tools.reasoning import ReasoningTools
from textwrap import dedent
import queue
import concurrent.futures
from agno.memory.v2.memory import Memory
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from tools.todoist_tools import TodoistTools
from tools.customer_tools import CustomerTools, get_customer
from tools.business_opportunities_tools import BusinessOpportunitiesTools, get_business
from tools.content_parse import convert_to_markdown, webpage_content_parse
from tools.get_link import get_link
from tools.ai_ppt import ai_ppt, upd_ppt_temp
from utils.knowledge.vector import get_user_lancedb, vector_add as util_vector_add, vector_del as util_vector_del, uri, tableName, searchType, embedder
from agno.agent import Agent, AgentKnowledge
from utils.tool import get_attachment_url, push_task_chunk
from utils.tool_name_map import tool_name_map
from utils.claude_models import get_random_claude_model
from utils.models import mainModel, deepseek, glm4
from utils.redis import get_client
from utils.agent_prompt import MAIN_INSTRUCTIONS, USER_INSTRUCTIONS, get_main_instructions, SESSION_INSTRUCTIONS
from tools.save_user_profile import save_user_profile, update_user_profile
from utils.doc_parse import read_document_from_url
from utils.models import openrouterClaude, gemini25, gpt41, openrouterGpt, KimiK2
# from tools.baidu_search import web_search
from tools.search_sougou import web_search
from tools.deepsearch_tools import deep_web_search_agent
from tools.sales_advisor import sales_advisor_rule
from datetime import datetime
from utils.tool import get_today_weekdays, get_next_weekday
from utils.error_notice import sendErrorNotice
from tools.crawl4ai import Crawl4aiTools
from utils.queue import task_queue
from agno.tools.mcp import MultiMCPTools
from dashscope import Application

load_dotenv()

memory = Memory(db=SqliteMemoryDb(
    table_name="team_memories", db_file="./memory.db"))

redisClient = get_client()

# 设置任务结果的过期时间（24小时）
TASK_RESULT_EXPIRE = 86400

# 确保结果目录存在
RESULTS_DIR = "task_results"
if not os.path.exists(RESULTS_DIR):
    os.makedirs(RESULTS_DIR)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时运行
    asyncio.create_task(cache_cleaner())

    # 设置应用日志级别为DEBUG
    logger.setLevel(logging.DEBUG)
    for handler in logger.handlers:
        handler.setLevel(logging.DEBUG)

    # 配置agno的debug日志输出到专用日志文件
    if os.getenv('APP_ENV') != "dev":
        configure_agno_logging(
            level=logging.DEBUG,
            log_dir='logs',
            log_file='agno_debug.log',
            preserve_console_handler=True  # 保留原始的彩色控制台输出
        )

    await task_queue.start()

    yield
    # 关闭时运行
    await task_queue.graceful_shutdown()  # 优雅关闭任务队列

# 创建FastAPI应用
app = FastAPI(
    title="销售助理API",
    description="销售助理团队API，提供待办任务管理、销售线索处理等功能",
    version="1.0.0",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 创建线程池，用于并行处理任务
# 设置最大工作线程数，可以根据服务器性能调整
MAX_WORKERS = int(os.getenv("TASK_WORKER_NUM"))
task_executor = concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS)


async def logger_hook(
    function_name: str, function_call: Callable, arguments: Dict[str, Any]
):
    """Log the duration of the function call"""
    start_time = time.time()

    # Call the function
    # logger.info(f"开始执行工具调用: {function_name}, 参数: {arguments}")
    result = function_call(**arguments) if not asyncio.iscoroutinefunction(function_call) else await function_call(**arguments)

    end_time = time.time()
    duration = end_time - start_time

    logger.info(f"工具调用 {function_name} 执行完成，耗时: {duration:.2f} 秒")

    # 统计每个用户和总的工具调用次数到redis
    user_id = get_context("user_id")
    redisClient.hincrby(f"tool_calls_count:{user_id}", function_name, 1)
    redisClient.hincrby("tool_calls_count:total", function_name, 1)

    # Return the result
    return result

def process_task_by_teacher(task_id, query, user_id, session_id, app_id):
    """
    老师分身agent处理任务
    """
    stop_after_tool_call = True
    try:
        responses = Application.call(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            app_id=app_id,
            prompt=query,
            stream=True,  # 流式输出
            incremental_output=True, # 增量输出
            session_id=session_id,
        )

        content = ""
        for response in responses:
            if response.status_code != HTTPStatus.OK:
                raise Exception(
                    f"请求失败, request_id={response.request_id}, code={response.status_code}, message={response.message}")
            else:
                # if response.output.text == "$":
                #     return "根据已知信息无法回答您的问题"
                content += response.output.text
                if stop_after_tool_call:
                    push_task_chunk(task_id, "RunResponse", response.output.text)
        logger.info(f"老师分身agent处理任务成功(session={session_id}): {content}")
        if stop_after_tool_call:
            push_task_chunk(task_id, "RunCompleted", content)
        return content
    except Exception as e:
        logger.error(f"老师分身agent处理任务失败(session={session_id}): {traceback.format_exc()}")
        err = "任务处理失败，请稍后再试"
        push_task_chunk(task_id, "RunResponse", err)
        push_task_chunk(task_id, "RunCompleted", err)


async def process_task(task_id, query, user_id, session_id, agent_id, user_info, app_id = None):
    """处理单个任务的函数"""
    logger.info(f"开始处理任务: {task_id}, 用户id: {user_id}, 查询: {query}")

    # 设置上下文变量
    set_context("user_id", user_id)
    set_context("session_id", session_id)
    set_context("task_id", task_id)
    set_context("query", query[-1]["content"] if isinstance(query, list) else query)

    if app_id:
        return process_task_by_teacher(task_id=task_id, query=query, user_id=user_id, session_id=session_id, app_id=app_id)

    # 创建结果文件路径
    result_file = os.path.join(RESULTS_DIR, f"{task_id}.json")
    # 预先定义redis_key，确保错误处理块可以访问
    redis_key = f"ai_task:{task_id}"

    try:
        vector_db = await get_user_lancedb(user_id)

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        current_week = get_today_weekdays()
        next_week_monday = get_next_weekday(1)

        # 模型切换
        # curModel = get_random_claude_model()
        # curModel = gpt41
        curModel = openrouterGpt
        # curModel = openrouterClaude
        # curModel = gemini25

        # 白名单测试提示词
        logger.info(f"user_id: {user_id}, APP_ENV: {os.getenv('APP_ENV')}")

        # 从Redis获取白名单用户列表
        whitelist_users = []  # 默认白名单为空
        if os.getenv('APP_ENV') == "test":
            try:
                whitelist_data = redisClient.get("prompt_whitelist_users")
                if whitelist_data:
                    whitelist_users = json.loads(whitelist_data)
                else:
                    whitelist_users = []  # 默认白名单为空
            except Exception as e:
                logger.error(f"获取白名单失败: {e}")

        if int(user_id) in whitelist_users:

            logger.info("-- 白名单测试提示词")
            # 获取自定义提示词，如果为空则使用默认提示词
            custom_main_prompts = redisClient.get("main_prompts")
            custom_user_prompts = redisClient.get("user_prompts")

            # 如果自定义提示词为空或None，使用默认提示词
            if custom_main_prompts and custom_main_prompts.strip():
                main_instructions = dedent(custom_main_prompts)
            else:
                main_instructions = MAIN_INSTRUCTIONS
                logger.info("使用默认系统提示词（自定义提示词为空）")

            if custom_user_prompts and custom_user_prompts.strip():
                user_instructions = dedent(custom_user_prompts)
            else:
                user_instructions = USER_INSTRUCTIONS
                logger.info("使用默认用户提示词（自定义提示词为空）")

            curModelId = redisClient.get("cur_main_model")
            if curModelId == "gemini25pro":
                curModel = gemini25
            elif curModelId == "gpt4.1":
                curModel = openrouterGpt
            elif curModelId == "kimi-k2":
                curModel = KimiK2
            else:
                curModel = get_random_claude_model(curModelId)
        else:
            # main_instructions = get_main_instructions()
            main_instructions = MAIN_INSTRUCTIONS
            user_instructions = USER_INSTRUCTIONS

        # if int(user_id) in (19, 40000) and os.getenv('APP_ENV') == "test":
        #     logger.info("-- 白名单测试提示词")
        #     main_instructions = dedent(redisClient.get("main_prompts"))
        #     user_instructions = dedent(redisClient.get("user_prompts"))
        #     curModelId = redisClient.get("cur_main_model")
        #     if curModelId == "gemini25pro":
        #         curModel = gemini25
        #     elif curModelId == "gpt4.1":
        #         curModel = openrouterGpt
        #     else:
        #         curModel = get_random_claude_model(curModelId)
        # else:
        #     main_instructions = MAIN_INSTRUCTIONS
        #     user_instructions = USER_INSTRUCTIONS

        logger.info(f"------ 当前使用模型 {curModel.id}  ------")
        async with MultiMCPTools(
            [
                "npx -y @antv/mcp-server-chart",
            ],
            timeout_seconds=10,
            env={"CONTENT_IMAGE_SUPPORTED": "false"},
            include_tools={
                # antv chart
                "generate_bar_chart", "generate_column_chart", "generate_line_chart",
                "generate_pie_chart", "generate_funnel_chart", "generate_dual_axes_chart", "generate_histogram_chart",
                "generate_word_cloud_chart", "generate_radar_chart", "generate_treemap_chart", "generate_mind_map",
            },
            # exclude_tools={"generate_boxplot_chart", "generate_sankey_chart", "generate_venn_chart", "generate_violin_chart"},
        ) as mcp_tools:
            if agent_id == "002":
                mainAgent = Agent(
                    name="销售助手 Ivy",
                    agent_id="002",
                    model=curModel,
                    tools=[save_user_profile],
                    telemetry=False,
                    markdown=False,
                    show_tool_calls=True,  # 显示工具调用
                    stream_intermediate_steps=True,
                    debug_mode=True,  # 启用调试模式
                    add_datetime_to_instructions=True,
                    user_id=str(user_id),
                    session_id=str(session_id),
                    add_context=True,
                    add_history_to_messages=True,
                    num_history_runs=20,
                    storage=get_singlestore_storage(
                        table_name="main_agent_sessions"),
                    instructions=user_instructions,
                )
            else:
                context = {
                    "current_time": f"现在的时间是：{current_time},  今天是{current_week}, 下周的周一是：{next_week_monday}"}
                is_business_session = False
                if str(session_id).startswith("business-"):
                    is_business_session = True
                    bo_id = session_id.split("-")[1]
                    business_info = get_business(int(bo_id))
                    # 解析JSON并只取data字段
                    try:
                        business_data = json.loads(business_info).get("data", {})
                        customer_id = business_data.get("customer_id", 0)
                        if customer_id:
                            customer_info = get_customer(customer_id)
                            customer_data = json.loads(customer_info).get("data", [])
                            customer_data = customer_data[0] if customer_data else {}
                            if customer_data: business_data["customer_info"] = {
                                "company_name": customer_data.get("company_name", ""),
                                "company_short_name": customer_data.get("company_short_name", ""),
                                "contact_name": customer_data.get("contact_name", ""),
                                "contact_title": customer_data.get("contact_title", ""),
                                "phone": customer_data.get("phone", ""),
                            }
                        context["当前会话在聊商机信息"] = business_data
                    except json.JSONDecodeError:
                        context["当前会话在聊商机信息"] = business_info
                    main_instructions = SESSION_INSTRUCTIONS
                mainAgent = Agent(
                    name="AI销售助手 Ivy",
                    agent_id="001",
                    # model=mainModel,
                    model=curModel,
                    tool_hooks=[logger_hook],
                    tools=[
                        ReasoningTools(
                            think=True,
                            analyze=True,
                            add_instructions=True,
                        ),
                        mcp_tools,
                        Crawl4aiTools(max_length=100000),
                        # extract_image_content,# 合并到文件解析工具中
                        convert_to_markdown,
                        # webpage_content_parse,
                        get_link,
                        # web_search,
                        deep_web_search_agent,
                        update_user_profile,
                        sales_advisor_rule,
                        TodoistTools(),
                        CustomerTools(),
                        BusinessOpportunitiesTools(),
                        AttachmentTools(),
                        KnowledgeTools(),
                        # EnterpriseTools(),
                        ai_ppt,
                        upd_ppt_temp,
                        # LocationTools(),
                        small_content_writer,
                        ChatTools(),
                        generate_mermaid_chart,
                    ],
                    telemetry=False,
                    markdown=True,
                    show_tool_calls=True,  # 显示工具调用
                    stream_intermediate_steps=True,
                    debug_mode=os.getenv(
                        "DEBUG_MODE", "False") == "True",  # 启用调试模式
                    user_id=str(user_id),
                    session_id=str(session_id),
                    additional_context=f"<user_info>这是我的个人信息：\n{user_info}</user_info>",
                    context=context,
                    # context={"user_info": f"这是我的个人信息：\n{user_info}",
                    #          "current_time": f"现在的时间是：{current_time}"},
                    add_context=True,
                    add_history_to_messages=True,
                    num_history_runs=bs_num_history_runs if is_business_session else num_history_runs,
                    storage=get_singlestore_storage(
                        table_name="main_agent_sessions"),
                    instructions=main_instructions,
                    knowledge=AgentKnowledge(
                        vector_db=vector_db,
                    ),
                    search_knowledge=True,
                )

            logger.info("----user_id: %s, session_id: %s----" %
                        (user_id, session_id))

            # 执行查询
            results = []

            # 处理查询
            user_query = ""
            if isinstance(query, list):  # 多条消息
                messages = query
                query = None
                user_query = messages[-1]["content"]
            else:  # 单条消息
                messages = None
                user_query = query
            r = await mainAgent.arun(query, stream=True, stream_intermediate_steps=True, show_full_reasoning=True, messages=messages)

            # 打印发送的tools function 数据
            # logger.info(
            #     f"发送给模型的工具数据: {json.dumps(mainAgent._tools_for_model, indent=2, ensure_ascii=False)}")

            tools = set()  # 去重
            async for chunk in r:
                is_stop = redisClient.get(f"ai_task_stop:{task_id}")
                # 将每个响应块保存到结果列表
                results.append(chunk)

                # 将 chunk 写入 Redis
                # 使用 task_id 作为 key 的前缀
                # redis_key 已在函数开始处定义
                # 只保留需要的字段
                chunk_data = {
                    "content": chunk.content if hasattr(chunk, 'content') else "",
                    "content_type": chunk.content_type if hasattr(chunk, 'content_type') else "",
                    "event": chunk.event if hasattr(chunk, 'event') else "",
                    "member_id": "",
                    "member_name": "",
                    "tool": {

                    }
                }
                if chunk_data["event"] == "RunResponseContent":
                    chunk_data["event"] = "RunResponse"
                # 处理工具调用事件
    #             if hasattr(chunk, 'event') and chunk.event not in ['RunResponse']:
    #                 if hasattr(chunk, 'tools') and chunk.tools:
    #                     for tool in chunk.tools:
    #                         data = {
    #                             "tool_call_id": tool.tool_call_id if hasattr(tool, 'tool_call_id') else None,
    #                             "tool_name": tool.tool_name if hasattr(tool, 'tool_name') else None,
    #                             "tool_args": tool.tool_args if hasattr(tool, 'tool_args') else None,
    #                             "tool_result": tool.result if (hasattr(tool, 'result') and tool.tool_name == "web_search") else None,
    #                         }
    #                         if json.dumps(data) not in tools:  # 去重
    #                             tools.add(json.dumps(data))  # 去重
    #                             logger.info("工具调用: " + str(tool))
    #                             if data["tool_name"] in tool_name_map:  # 工具名称映射
    #                                 data["tool_name"] = tool_name_map[data["tool_name"]]
    #                             chunk_data["tool"] = data
    #                             chunk_data["member_name"] = data["tool_name"]
    #                             chunk_data["member_id"] = data["tool_call_id"]

                # 处理工具调用事件
                if hasattr(chunk, 'event') and chunk.event not in ['RunResponse']:
                    if hasattr(chunk, 'tool') and chunk.tool:
                        # for tool in chunk.tools:
                        tool = chunk.tool
                        tool_name = tool.tool_name if hasattr(tool, 'tool_name') else None
                        # 从上下文获取完整工具结果(用于需要保存结果但返回给agent的结果不完整的处理)
                        if chunk.event == "ToolCallCompleted" and tool_name in ("deep_web_search_agent"):
                            tool_result = get_context(tool_name + "_tool_result")
                            set_context(tool_name + "_tool_result", "") # 清理上下文
                            if tool_result: tool.result = tool_result
                        data = {
                            "tool_call_id": tool.tool_call_id if hasattr(tool, 'tool_call_id') else None,
                            "tool_name": tool_name,
                            "tool_args": tool.tool_args if hasattr(tool, 'tool_args') else None,
                            "tool_result": tool.result if (hasattr(tool, 'result') and tool_name in ("web_search", "small_content_writer", "deep_web_search_agent")) else None,
                        }

                        if json.dumps(data) not in tools:  # 去重
                            tools.add(json.dumps(data))  # 去重
                            logger.info("工具调用: " + str(tool))
                            if data["tool_name"] in tool_name_map:  # 工具名称映射
                                data["tool_name"] = tool_name_map[data["tool_name"]]
                            chunk_data["tool"] = data
                            chunk_data["member_name"] = data["tool_name"]
                            chunk_data["member_id"] = data["tool_call_id"]

                if is_stop:
                    chunk_data = {
                        "content": "".join([chunk.content for chunk in results if chunk.event == "RunResponse"]),
                        "content_type": "str",
                        "event": "RunCompleted",
                        "member_id": "",
                        "member_name": "",
                        "tool": {

                        }
                    }

                # 将 chunk 转换为 JSON 字符串
                chunk_json = json.dumps(
                    chunk_data, ensure_ascii=False, default=str)
                # 将 chunk 添加到 Redis List 的右侧
                redisClient.rpush(redis_key, chunk_json)
                # 设置过期时间
                redisClient.expire(redis_key, TASK_RESULT_EXPIRE)
                if chunk_data["event"] == "RunStarted":
                    await send_message_to_user()

                # 同时写入文件(使用临时文件，避免写入冲突)
                temp_file = os.path.join(RESULTS_DIR, f"{task_id}_temp.json")
                try:
                    # 创建可序列化的结果列表
                    serializable_results = []
                    for result_chunk in results:
                        if hasattr(result_chunk, 'to_dict'):
                            # 如果对象有to_dict方法，使用它
                            serializable_results.append(result_chunk.to_dict())
                        elif isinstance(result_chunk, dict):
                            # 如果是字典，直接添加
                            serializable_results.append(result_chunk)
                        elif isinstance(result_chunk, tuple):
                            # 处理元组
                            tuple_dict = {}
                            for i, item in enumerate(result_chunk):
                                tuple_dict[f"item_{i}"] = str(item)
                            serializable_results.append(tuple_dict)
                        else:
                            # 其他情况，只保留基本信息
                            try:
                                basic_chunk = {
                                    "content": result_chunk.content if hasattr(result_chunk, 'content') else str(result_chunk),
                                    "content_type": result_chunk.content_type if hasattr(result_chunk, 'content_type') else "unknown",
                                    "event": result_chunk.event if hasattr(result_chunk, 'event') else "unknown"
                                }
                                serializable_results.append(basic_chunk)
                            except Exception as chunk_err:
                                # 如果无法提取属性，将对象转换为字符串
                                serializable_results.append(
                                    {"content": str(result_chunk)})

                    with open(temp_file, 'w', encoding='utf-8') as f:
                        json.dump(serializable_results, f, ensure_ascii=False,
                                  indent=2, default=str)
                    # 重命名为正式文件(原子操作，避免读取未完成的文件)
                    os.replace(temp_file, result_file)
                except Exception as e:
                    logger.error(f"写入结果文件时出错: {str(e)}")
                if is_stop:
                    break
            # 更新任务状态
            logger.info(f"任务 {task_id} 已" + ("终止" if is_stop else "完成"))

    except Exception as e:
        # 处理错误
        error_msg = f"处理任务时出错: uid={user_id}, query={query}, {traceback.format_exc()}"
        logger.error(error_msg)

        # 判断是否为上下文超长错误
        if "This model's maximum context length" in error_msg:
            chunk_content = "内容超限，请分批次发送或精简后重试。"
        else:
            chunk_content = "Ivy正在升级优化，建议10分钟后重试。"

        chunk_data = {
            "content": chunk_content,
            "content_type": "str",
            "event":  "RunCompleted"
        }
        # 将 chunk 转换为 JSON 字符串
        chunk_json = json.dumps(chunk_data, ensure_ascii=False, default=str)
        # 将 chunk 添加到 Redis List 的右侧
        redisClient.rpush(redis_key, chunk_json.replace(
            'RunCompleted', 'RunResponse'))
        redisClient.rpush(redis_key, chunk_json)
        redisClient.expire(redis_key, TASK_RESULT_EXPIRE)

        # 将错误写入结果文件
        # 处理线程内部异常
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({"error": error_msg}, f, ensure_ascii=False, indent=2)

        # 发送错误告警
        if os.getenv('APP_ENV') != "dev":
            sendErrorNotice("python处理任务出错",  error_msg)

# 任务包装函数，用于在线程池中运行


def task_wrapper(task_id, query, user_id, session_id, agent_id, user_info, app_id = None):
    """包装异步函数，使其可以在线程池中运行"""
    logger.info(f"task_wrapper: {task_id} 开始运行, 用户id: {user_id}")
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(process_task(
            task_id, query, user_id, session_id, agent_id, user_info, app_id))
    except Exception as e:
        logger.error(f"任务执行出错: {str(e)}")
    finally:
        try:
            # 等待所有任务完成
            pending = asyncio.all_tasks(loop)
            if pending:
                # 设置超时时间以避免无限等待
                loop.run_until_complete(asyncio.gather(
                    *pending, return_exceptions=True))

            # 添加一个小延迟，确保资源正确释放
            loop.run_until_complete(asyncio.sleep(0.250))

            # 关闭异步资源（这是关键修复）
            for task in pending:
                if not task.done():
                    task.cancel()
                    try:
                        # 给每个任务一个取消自己的机会
                        loop.run_until_complete(
                            asyncio.wait_for(task, timeout=1.0))
                    except (asyncio.CancelledError, asyncio.TimeoutError):
                        pass
                    except Exception as e:
                        logger.error(f"取消任务时出错: {str(e)}")
        except Exception as e:
            logger.error(f"清理事件循环资源时出错: {str(e)}")
        finally:
            try:
                # 安全关闭事件循环
                loop.run_until_complete(loop.shutdown_asyncgens())
                loop.close()
            except Exception as e:
                logger.error(f"关闭事件循环时出错: {str(e)}")


@app.post("/save_parse_file")
async def save_parse_file(request: Request):
    """
    保存用户解析后的文档
    """
    data = await request.json()
    url = data.get("url")
    user_id = data.get("user_id")
    attachment_id = data.get("attachment_id")
    file_type = data.get("file_type")
    file_name = data.get("file_name")
    if not url or not user_id or not attachment_id or not file_type or not file_name:
        raise HTTPException(
            status_code=400, detail="URL, user_id, file_type, file_name and attachment_id are required")

    task = asyncio.create_task(write_user_file_by_url(
        read_url=url, file_type=file_type, user_id=user_id, file_id=attachment_id, file_name=file_name, view_url=get_attachment_url(attachment_id)))
    return {"task_id": task.get_name(), "status": "success", "message": "任务已创建"}


@app.post("/del_parse_file")
async def del_parse_file(request: Request):
    data = await request.json()
    user_id = data.get("user_id")
    attachment_ids = data.get("attachment_ids")
    if not user_id or not attachment_ids:
        raise HTTPException(
            status_code=400, detail="user_id and attachment_ids are required")

    for attachment_id in attachment_ids:
        if not delete_user_file(user_id=user_id, file_id=attachment_id):  # 删除本地文件
            return {"status": "error", "message": f"删除本地文件失败(attachment_id={attachment_id})"}
    return {"status": "success", "message": "删除文档成功"}


@app.post("/vector_add")
async def vector_add(request: Request):
    """
    文档写入向量接口，接收文档URL，将文档内容写入向量数据库。
    """
    data = await request.json()
    url = data.get("url")
    user_id = data.get("user_id")
    attachment_id = data.get("attachment_id")
    file_type = data.get("file_type")
    file_name = data.get("file_name")
    save_parse_file = data.get("save_parse_file", False)
    if not url or not user_id or not attachment_id or not file_type or not file_name:
        raise HTTPException(
            status_code=400, detail="URL, user_id, file_type, file_name and attachment_id are required")

    task = asyncio.create_task(util_vector_add(
        url=url, file_type=file_type, user_id=user_id, attachment_id=attachment_id, file_name=file_name, save_parse_file=save_parse_file))
    return {"task_id": task.get_name(), "status": "success", "message": "任务已创建"}


@app.post("/vector_del")
async def vector_del(request: Request):
    """
    文档删除向量接口，接收文档向量ID列表，将对应的文档内容从向量数据库中删除。
    """
    data = await request.json()
    ids = data.get("ids")
    user_id = data.get("user_id")
    attachment_ids = data.get("attachment_ids", [])
    if not user_id or (not ids and not attachment_ids):
        raise HTTPException(
            status_code=400, detail="user_id and (ids or attachment_ids) are required")

    # 删除速度比较快可以等待结果返回
    result = await util_vector_del(ids=ids, user_id=user_id) if ids else {"status": "success", "message": "删除文档向量成功"}
    for attachment_id in attachment_ids:
        if not delete_user_file(user_id=user_id, file_id=attachment_id):  # 删除本地文件
            return {"status": "error", "message": f"删除本地文件失败(attachment_id={attachment_id})"}
    return result


@app.post("/doc_parse")
async def doc_parse(request: Request):
    """
    文档内容解析
    """
    data = await request.json()
    url = data.get("url")
    file_type = data.get("file_type")
    read_size = data.get("read_size")
    if not url or not file_type:
        raise HTTPException(
            status_code=400, detail="url and file_type are required")

    try:
        result = read_document_from_url(url=url, file_type=file_type)
    except Exception as e:
        return {
            "status": "error",
            "message": f"文档解析失败：{str(e)}"
        }
    return {
        "status": "success",
        "message": "文档解析成功",
        "data": {"content": result if not read_size else result[:read_size]}
    }


@app.post("/task")
async def create_task(request: Request):
    """创建一个异步任务，将查询请求提交给线程池处理，并返回任务ID"""

    taskItem = await request.json()

    task_id = taskItem.get("task_id")
    session_id = taskItem.get("session_id")
    user_id = taskItem.get("user_id")
    query = taskItem.get("query")
    agent_id = taskItem.get("agent_id")
    user_info = taskItem.get("user_info")
    app_id = taskItem.get("app_id")

    logger.info(f"agent_id: {agent_id}")
    # 将任务提交给线程池执行
    task_executor.submit(task_wrapper, task_id, query,
                         user_id, session_id, agent_id, user_info, app_id)

    logger.info(f"用户id: {user_id}")

    # 返回任务ID和状态
    return {
        "task_id": task_id,
        "status": "已创建",
        "message": "任务已提交，请使用任务ID查询结果",
        "current_workers": f"当前活跃工作线程: {len([t for t in task_executor._threads if t.is_alive()])}/{MAX_WORKERS}"
    }


@app.get("/health")
async def health_check(request: Request):
    try:
        mysql_start = time.time()
        mysql_connection = get_async_mysql()
        async with mysql_connection() as session:
            await session.execute(text("SELECT 1"))
        mysql_end = time.time()
        redisClient.ping()
        redis_end = time.time()
        # 总耗时超过500ms时打印日志记录耗时
        total_time = redis_end - mysql_start
        if total_time > 0.5:
            logger.warning(
                f"health_check mysql耗时: {int((mysql_end - mysql_start) * 1000)}ms, redis耗时: {int((redis_end - mysql_end) * 1000)}ms, 总耗时: {int(total_time * 1000)}ms")

        return {"status": "ok"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    import signal
    import sys

    # 定义信号处理函数，用于优雅退出
    def signal_handler(sig, frame):
        print("\n正在优雅地关闭服务...")
        # 关闭线程池
        task_executor.shutdown(wait=False)
        # 退出程序
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

    # 显示启动信息
    print("=" * 50)
    print("销售助理API服务正在启动...")
    print("=" * 50)

    # 启动服务器
    uvicorn.run(app, host="0.0.0.0", port=int(os.getenv('APP_SERVER_PORT')))

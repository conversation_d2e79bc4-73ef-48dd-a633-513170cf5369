import traceback
from agno.tools import Toolkit
from httpx import Response
import pymysql
from typing import List, Dict, Optional, Any
from datetime import datetime
import json
import logging
import requests
from .attachment_tools import generate_excel
from utils.knowledge.user_file_storage import read_user_files
from utils.context import get_context
from utils.doc_parse import read_document_from_url
from utils.logger import logger
from utils.pyapi import call_api
from utils.models import openrouterGpt, deepseek
from agno.models.message import Message
from agno.agent import Agent
from pydantic import BaseModel, Field
from tools.reasoning import ReasoningTools
from utils.excel import ExcelConverter
import os
from dotenv import load_dotenv
load_dotenv()

IMPORT_TMP_URL = "https://profly-website.oss-cn-shenzhen.aliyuncs.com/template.xlsx"
    
class CustomerFieldMapModel(BaseModel):
    field_map: Dict[str, int] = Field(..., description="""字段映射字典，key为系统字段名，value为该字段对应的数据在表格中所在的数据列编号(从0开始)，所有系统字段名和含义如下：
    company_name: 客户公司简称
    company_full_name: 客户公司名称全称
    contact_name: 联系人姓名
    contact_title: 联系人职位
    phone: 联系人电话，手机号
    contact_remark: 联系人备注
    contact_birthday: 联系人生日
    contact_profile: 联系人画像
    notes: 公司备注
""")

def add_customer(customer_list: List[dict]) -> str:
    """批量添加新的客户信息，名片信息 (根据已有的信息，如果单个客户信息只有contact_name，也可以直接创建客户信息; 单个客户信息中没有提取到参数对应的值，留空就可以,不要再向用户提问，不要编造数据，优先提取中文信息)

    Args:
        customer_list (List[Dict[str, Any]]): 客户信息列表, 每个元素是一个字典，包含下面的字段:
            company_name (str): 客户公司简称(可以从公司全称提炼4-6个字左右当作简称)
            company_full_name (str): 客户公司名称全称
            contact_name (str): 联系人姓名
            contact_title (str): 联系人职位
            phone (str): 联系人电话，手机号
            contact_remark (str): 联系人备注（邮箱,微信等）
            contact_birthday (str): 联系人生日(yyyy-mm-dd)
            contact_profile (str): 联系人画像(分析整合联系人个性特征、行为趋势及战略价值，提供针对性的跟进建议)
            customer_level (str): 客户级别：潜在客户，普通客户，重点客户
            notes (str): 公司备注(公司地址，业务类型，网站地址等)

    Returns:
        str: JSON格式的响应结果,包含客户ID 和链接 
    """
    data: dict[str, Any] = {
        "data_list": customer_list,
    }
    return call_api("/pyapi/customer/add", data)

def export_customer(file_name: str, customer_ids: list[int] = []) -> str:
    """
    导出客户信息
    Args:
        file_name (str): 导出的文件名称
        customer_ids (list[int]): 要导出的客户ID列表，默认导出所有客户
    Returns:
        str: 响应结果,包含文件ID 和链接
    """
    try:
        if f".xlsx" not in file_name:
            file_name = f"{file_name}.xlsx"
        result = json.loads(call_api("/pyapi/customer/list", {"ids": customer_ids}))
        if result.get("error"):
            return str({"status": "error", "message": result.get("error")})
        customers = result.get("data", [])
        if not customers:
            return str({"status": "error", "message": "没有要导出的客户信息"})
        table = {
            "title": file_name.split(".")[0],
            "header": ["公司名称", "联系人姓名", "联系人电话", "客户级别", "创建时间"],
            "data": [[customer["company_name"], customer["contact_name"], customer["phone"], customer["customer_level"], customer["created_at"]] for customer in customers]
        }
        return generate_excel([table], file_name)
    except Exception as e:
        logger.error(f"导出客户失败: {traceback.format_exc()}")
        return str({"status": "error", "message": f"导出客户失败: {e}"})

async def _ai_import_customer(query: str, file_url: str, file_type: str = "xlsx") -> str:
    """
    通过excel智能导入客户信息
    Args:
        query (str): 导入客户信息的描述要求, 用于指导模型进行个性化导入
        file_url (str): 要导入的客户文件url
        file_type (str, optional): 文件类型(后缀名), 默认为xlsx
    Returns:
        str: 响应结果,包含客户ID 和链接
    """
    try:
        if file_url:
            customer_list = read_document_from_url(file_url, file_type)
        logger.info(f"客户信息共{len(customer_list)}字")

        # 调用大模型
        mainAgent = Agent(
            name="销售助手 Ivy",
            agent_id="002",
            model=deepseek,
            tools=[add_customer],
            telemetry=False,
            markdown=False,
            show_tool_calls=True,  # 显示工具调用
            stream_intermediate_steps=True,
            debug_mode=True,  # 启用调试模式
            instructions="""你是一个专业的销售领域导入助手, 负责将销售客户信息导入到系统中，除非用户有明确要求，否则你必须将所有客户信息尽可能详细全部导入到系统中，导入后把结果原封不动的返回回来""",
        )
        r = await mainAgent.arun(f"{query}\n\n从Excel提取的markdown表格数据:\n\n{customer_list}", stream=True, stream_intermediate_steps=True, show_full_reasoning=True)
        content = ""
        async for chunk in r:
            content += chunk.content if hasattr(chunk, 'content') and chunk.content else ""
        logger.info(f"导入客户信息结果: {content}")
        return content
    except Exception as e:
        logger.error(f"导入客户失败: {traceback.format_exc()}")
        return str({"status": "error", "message": f"导入客户失败: {e}"})

def import_customer(file_url: str, file_type: str = "xlsx") -> str:
    """
    通过excel导入客户信息
    Args:
        file_url (str): 要导入的客户文件url
        file_type (str, optional): 文件类型(后缀名), 默认为xlsx
    Returns:
        str: 响应结果,包含客户ID 和链接
    """
    try:
        # user_id = get_context("user_id")
        if file_url:
            customer_list = read_document_from_url(file_url, file_type)
        logger.info(f"客户信息共{len(customer_list)}字")

        tables = ExcelConverter().parse_markdown_tables(customer_list)
        if not tables:
            return str({"status": "error", "message": "未识别到客户信息表格数据, 请参考导入模板修正后重试:" + IMPORT_TMP_URL})
        if len(tables) > 1:
            return str({"status": "error", "message": "识别到多个表格数据, 请参考导入模板合并到一个表格或删除多余表格后重试:" + IMPORT_TMP_URL})
        table = tables[0]
        if len(table["header"]) > 10:
            return str({"status": "error", "message": "表格数据列过多, 请参考导入模板删除多余列, 保留10列以内后重试:" + IMPORT_TMP_URL})

        # 调用大模型
        prompt = f"你是一个专业的表格字段识别助手，负责通过表格中的表头和数据识别出表格数据列与系统字段的映射关系并严格按照定义的json格式输出, 下面是markdown表格数据:\n\n" + "\n".join(customer_list.split("\n")[:5])
        logger.info(f"开始识别客户信息字段映射: {prompt}")

        response = openrouterGpt.response(
            messages=[Message(role="user", content=prompt)],
            response_format=CustomerFieldMapModel
        )
        content = response.content
        logger.info(f"客户信息表格字段识别结果: {content}")
        if not content:
            return str({"status": "error", "message": "未识别到客户信息表格字段, 请参考导入模板修正后重试:" + IMPORT_TMP_URL})
        field_map = CustomerFieldMapModel.model_validate_json(content).field_map
        if "company_name" not in field_map and "company_full_name" not in field_map and "contact_name" not in field_map:
            return str({"status": "error", "message": "未识别到客户公司名称或联系人姓名字段, 请参考导入模板修正后重试:" + IMPORT_TMP_URL})
        
        customers = []
        for row in table["data"]:
            customer = {}
            for key, value in field_map.items():
                value = int(value)
                if value < 0 or value >= len(row):
                    return str({"status": "error", "message": "客户信息表格数据列与字段映射不匹配, 请参考导入模板修正后重试:" + IMPORT_TMP_URL})
                customer[key] = row[value]
            if customer: customers.append(customer)
        if not customers:
            return str({"status": "error", "message": "未识别到客户信息, 请参考导入模板修正后重试:" + IMPORT_TMP_URL})
        return add_customer(customers)

    except Exception as e:
        logger.error(f"导入客户失败: {traceback.format_exc()}")
        return str({"status": "error", "message": f"导入客户失败: {e}, 请参考导入模板修正后重试:" + IMPORT_TMP_URL})

def get_customer(customer_id: int | str) -> str:
    """获取客户详细信息, 支持同时获取多个客户信息 
    Args:
        customer_id: 客户ID，多个id时用,间隔
    """
    try:
        user_id = get_context("user_id")
        response = requests.get(
            f"{os.getenv('API_URL')}/pyapi/customer/detail?customer_id={customer_id}&user_id={user_id}")
        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(f"获取客户详细信息失败: {str(response.text)}")
            return json.dumps({"error": "获取客户详细信息失败."}, ensure_ascii=False)

    except Exception as e:
        logger.error(f"获取客户信息失败: {str(e)}")
        return json.dumps({
            "status": "error",
            "message": f"获取客户信息失败: {str(e)}"
        }, ensure_ascii=False)

class CustomerTools(Toolkit):
    """用于管理客户信息的工具集合"""

    def __init__(self):
        super().__init__(name="customer_tools")

        # 注册工具函数
        self.register(add_customer)
        self.register(self.update_customer)
        self.register(self.busi_bind_customer)
        self.register(self.delete_customer)
        self.register(self.list_customers)
        self.register(get_customer)
        self.register(self.sort_customer)
        self.register(import_customer)
        self.register(export_customer)
        logger.info("CustomerTools工具注册完成")

    def update_customer(self,
                        customer_id: str,
                        company_name: str = "",
                        company_full_name: str = "",
                        contact_name: str = "",
                        contact_title: str = "",
                        contact_birthday: str = "",
                        contact_profile: str = "",
                        phone: str = "",
                        customer_level: str = "",
                        contact_remark: str = "",
                        notes: str = "",
                        ) -> str:
        """更新客户信息

        Args:
            customer_id (str): 客户ID
            company_name (str): 客户公司简称
            company_full_name (str): 客户公司全称,
            contact_name (str): 联系人姓名
            contact_title (str): 联系人职位
            contact_birthday (str): 联系人生日(yyyy-mm-dd), 没有生日时为"0000-00-00"
            contact_profile (str): 联系人画像(分析整合联系人个性特征、行为趋势及战略价值，提供针对性的跟进建议)
            phone (str): 联系人电话，手机号
            customer_level (str): 客户级别：潜在客户，普通客户， 重点客户
            contact_remark (str): 联系人备注
            notes (str): 公司备注(公司地址，业务类型，网站地址等)

        """
        try:
            data: dict[str, Any] = {
                "customer_id": customer_id,
                "company_name": company_name,
                "company_full_name": company_full_name,
                "contact_name": contact_name,
                "contact_title": contact_title,
                "contact_birthday": contact_birthday,
                "contact_profile": contact_profile,
                "phone": phone,
                "customer_level": customer_level,
                "contact_remark": contact_remark,
                "notes": notes,
                "user_id": get_context("user_id")
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/customer/edit", data=data)

            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"更新客户信息失败: {str(response.text)}")
                return json.dumps({"error": "更新客户信息失败."}, ensure_ascii=False)

        except Exception as e:
            logger.error(f"更新客户信息失败: {str(response.text)}")
            logger.error(f"更新客户信息失败: {str(e)}")
            return str({"status": "error", "message": f"更新客户信息失败: {str(e)}"})

    def busi_bind_customer(self, bo_id: int, customer_id: int) -> str:
        """将商机关联客户(一个客户可以有多个商机)

        Args:
            customer_id (int): 客户ID
            bo_id (int): 商机ID

        """

        try:
            data: dict[str, Any] = {
                "bo_id": bo_id,
                "customer_id": customer_id,
                "user_id": get_context("user_id")
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/busiopp/bindCustomer", data=data)

            if response.status_code == 200:
                res = response.json()
                return json.dumps(res, ensure_ascii=False)
            else:
                logger.error(f"商机关联客户失败:{str(response.text)}")
                return json.dumps({
                    "status": "error",
                    "message": "将商机与客户关联"
                }, ensure_ascii=False)
        except Exception as e:
            logger.error(f"将商机与客户关联: {str(e)}")
            logger.error(f"商机关联客户失败:{str(response.text)}")
            return json.dumps({
                "status": "error",
                "message": f"将商机与客户关联: {str(e)}"
            }, ensure_ascii=False)

    def list_customers(self) -> str:
        """
        列出所有客户列表，包含customer_id客户id

        """
        try:
            user_id = get_context("user_id")
            response = requests.get(
                f"{os.getenv('API_URL')}/pyapi/customer/list?user_id={user_id}")
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"列出客户信息失败: {str(response.text)}")
                return json.dumps({"error": "列出所有客户列表失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"列出客户信息失败: {str(e)}")
            return str({"status": "error", "message": f"列出客户信息失败: {str(e)}"})

    def delete_customer(self,
                        customer_id: int | str,
                        ) -> str:
        """删除客户信息,支持批量删除

        Args:
            customer_id: 客户ID，多个id时用,间隔

        Returns:
            str: 包含操作结果的JSON字符串
        """
        try:
            data: dict[str, Any] = {
                "customer_id": customer_id,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/customer/del", data=data)
            if response.status_code == 200:
                res = response.json()
                if res["code"] == 0:
                    return str({
                        "status": "success",
                        "message": f"成功删除客户: {customer_id}",
                    })
                elif res["code"] == 1:
                    return str({
                        "status": "error",
                        "message": f"{customer_id}这个客户信息不存在",
                    })
            else:
                logger.error(f"删除客户信息失败: {str(response.text)}")
                return str({"error": "删除客户信息失败."})
        except Exception as e:
            logger.error(f"删除客户信息失败: {str(e)}")
            return str({"status": "error", "message": f"删除客户信息失败: {str(e)}"})

    def sort_customer(self, customer_ids: str) -> str:
        """
        更新客户信息的排序顺序, 如果用户没有提供排序规则, 则按照下面规则排序

        # 通用排序提示词
        对客户数据进行智能排序，优先级从高到低：

        ## 排序规则
        1. 客户关联商机的记录，优先级显著提升。
        2. 客户级别为“重要客户”，优先级提升.
        3. 备注含“高潜力”“决策人”的记录，优先级略高。
        4. 创建日期较新的记录，优先级略高。

        ## 输出
        按优先级从高到低排序的记录列表，附带简要排序理由。

        Args:
            customer_ids:  按优先级从高到低排好的客户ID列表(优先级高的在前面)，每个id之间用,间隔

        Returns:
            str: 包含操作结果的JSON字符串
        """
        logger.info(f"排序客户 ID: {customer_ids}")
        try:
            if not customer_ids:
                return json.dumps({
                    "status": "error",
                    "message": "customer_ids不能为空"
                }, ensure_ascii=False)
            data: dict[str, Any] = {
                "ids": customer_ids.replace(" ", "").split(","),  # 去除空格并分割,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/customer/sortAll", json=data)
            if response.status_code == 200:
                return json.dumps(response.json(), ensure_ascii=False)
            else:
                logger.error(f"排序客户失败: {str(response.text)}")
                return json.dumps({"error": "排序客户失败."}, ensure_ascii=False)
        except Exception as e:
            logger.error(f"排序客户失败: {str(e)}")
            result = {
                "success": False,
                "message": f"排序客户失败: {str(e)}"
            }
            return json.dumps(result, ensure_ascii=False)

    def add_interaction(self,
                        customer_id: int,
                        interaction_type: str,
                        content: str,
                        next_action: Optional[str] = None
                        ) -> str:
        """添加客户互动记录

        Args:
            customer_id (int): 客户ID
            interaction_type (str): 互动类型：电话、邮件、微信，见面
            content (str): 互动内容
            next_action (str): 下一步行动
        """
        try:
            data: dict[str, Any] = {
                "customer_id": customer_id,
                "interaction_type": interaction_type,
                "content": content,
                "next_action": next_action,
                "user_id": get_context("user_id"),
            }
            response = requests.post(
                f"{os.getenv('API_URL')}/pyapi/customer/addInteract", data=data)

            if response.status_code == 200:
                res = response.json()
                return json.dumps(res, ensure_ascii=False)
            else:
                logger.error(f"添加互动记录失败:  {str(response.text)}")
                return json.dumps({
                    "status": "fail",
                    "message": f"添加客户互动记录失败",
                    "error": response.text
                }, ensure_ascii=False)

        except Exception as e:
            logger.error(f"添加互动记录失败: {str(e)}")
            return str({"status": "error", "message": f"添加互动记录失败: {str(e)}"})

    def get_interactions(self,
                         customer_id: str,
                         ) -> str:
        """获取客户的互动历史"""
        try:
            user_id = get_context("user_id")
            response = requests.get(
                f"{os.getenv('API_URL')}/pyapi/customer/listInteract?user_id={user_id}&customer_id={customer_id}")
            if response.status_code == 200:
                res = response.json()
                return str({
                    "status": "success",
                    "customers": res["data"],
                })
            else:
                logger.error(f"获取客户的互动历史失败: {str(response.text)}")
                return json.dumps({"error": "获取客户的互动历史失败."}, ensure_ascii=False)

        except Exception as e:
            logger.error(f"获取互动记录失败: {str(e)}")
            return str({"status": "error", "message": f"获取互动记录失败: {str(e)}"})

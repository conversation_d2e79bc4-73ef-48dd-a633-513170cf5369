import streamlit as st
import pandas as pd
from sqlalchemy import text
from datetime import datetime, timedelta, date
from utils.mysql import get_sync_mysql
import plotly.express as px


def _get_date_range(last_n_days: int = 7):
    """Return start (inclusive) and end (exclusive) datetimes covering last_n_days up to today end."""
    today = datetime.now().date()
    end_exclusive = datetime.combine(today + timedelta(days=1), datetime.min.time())
    start_inclusive = end_exclusive - timedelta(days=last_n_days)
    return start_inclusive, end_exclusive


def _fetch_overall_stats(session, start_at: datetime, end_at: datetime):
    query = text(
        """
        SELECT 
            COUNT(*) AS total_messages,
            COUNT(DISTINCT user_id) AS total_active_users
        FROM user_chat
        WHERE created_at >= :start_at AND created_at < :end_at
        """
    )
    row = session.execute(query, {"start_at": start_at, "end_at": end_at}).first()
    if row is None:
        return {"total_messages": 0, "total_active_users": 0}
    return {"total_messages": int(row.total_messages or 0), "total_active_users": int(row.total_active_users or 0)}


def _fetch_daily_stats(session, start_at: datetime, end_at: datetime) -> pd.DataFrame:
    query = text(
        """
        SELECT 
            DATE(created_at) AS day,
            COUNT(*) AS messages,
            COUNT(DISTINCT user_id) AS active_users
        FROM user_chat
        WHERE created_at >= :start_at AND created_at < :end_at
        GROUP BY DATE(created_at)
        ORDER BY day
        """
    )
    result = session.execute(query, {"start_at": start_at, "end_at": end_at})
    rows = [(r.day, int(r.messages), int(r.active_users)) for r in result]

    # Build full date index with zeros for missing days
    start_date = start_at.date()
    end_date = (end_at - timedelta(days=1)).date()
    all_days = pd.date_range(start=start_date, end=end_date, freq="D")
    df = pd.DataFrame(rows, columns=["date", "messages", "active_users"]) if rows else pd.DataFrame(columns=["date", "messages", "active_users"]) 
    if not df.empty:
        df["date"] = pd.to_datetime(df["date"]).dt.date
    df_full = pd.DataFrame({"date": all_days.date})
    df = df_full.merge(df, on="date", how="left").fillna({"messages": 0, "active_users": 0})
    df["messages"] = df["messages"].astype(int)
    df["active_users"] = df["active_users"].astype(int)
    return df


def _fetch_platform_stats(session, start_at: datetime, end_at: datetime) -> pd.DataFrame:
    query = text(
        """
        SELECT 
            COALESCE(platform, 'unknown') AS platform,
            COUNT(*) AS messages,
            COUNT(DISTINCT user_id) AS active_users
        FROM user_chat
        WHERE created_at >= :start_at AND created_at < :end_at
        GROUP BY platform
        ORDER BY messages DESC
        """
    )
    result = session.execute(query, {"start_at": start_at, "end_at": end_at})
    rows = [(r.platform, int(r.messages), int(r.active_users)) for r in result]
    return pd.DataFrame(rows, columns=["platform", "messages", "active_users"]) if rows else pd.DataFrame(columns=["platform", "messages", "active_users"]) 


def _fetch_top_users(session, start_at: datetime, end_at: datetime, limit: int = 20) -> pd.DataFrame:
    query = text(
        """
        SELECT 
            user_id,
            COUNT(*) AS messages
        FROM user_chat
        WHERE created_at >= :start_at AND created_at < :end_at
        GROUP BY user_id
        ORDER BY messages DESC
        LIMIT :limit
        """
    )
    result = session.execute(query, {"start_at": start_at, "end_at": end_at, "limit": limit})
    rows = [(int(r.user_id), int(r.messages)) for r in result]
    return pd.DataFrame(rows, columns=["user_id", "messages"]) if rows else pd.DataFrame(columns=["user_id", "messages"]) 


def _fetch_user_info_map(session, user_ids: list[int]) -> dict:
    if not user_ids:
        return {}
    placeholders = ",".join([f":user_id_{i}" for i in range(len(user_ids))])
    query = text(
        f"""
        SELECT id, name, mobile
        FROM user
        WHERE id IN ({placeholders})
        """
    )
    params = {f"user_id_{i}": uid for i, uid in enumerate(user_ids)}
    result = session.execute(query, params)
    info = {}
    for row in result:
        info[int(row.id)] = {"name": row.name or f"用户{row.id}", "phone": row.mobile or "-"}
    return info


def render_user_activity():
    st.header("👤 用户活跃统计")

    db_session = get_sync_mysql()

    # Date range selector (default: last 7 days)
    with st.container():
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            start_default, end_default = _get_date_range(7)
            start_date = st.date_input("开始日期", value=start_default.date(), key="ua_start_date")
        with col2:
            end_date = st.date_input("结束日期", value=(end_default - timedelta(days=1)).date(), key="ua_end_date")
        with col3:
            st.write("")
            st.write("")
            refresh = st.button("刷新", type="primary", use_container_width=True, key="ua_refresh")

    # Normalize to datetime bounds
    start_at = datetime.combine(start_date, datetime.min.time())
    end_at = datetime.combine(end_date + timedelta(days=1), datetime.min.time())

    with db_session() as session:
        # Fetch data
        overall = _fetch_overall_stats(session, start_at, end_at)
        daily_df = _fetch_daily_stats(session, start_at, end_at)
        platform_df = _fetch_platform_stats(session, start_at, end_at)
        top_users_df = _fetch_top_users(session, start_at, end_at, limit=20)

        # Overview metrics
        st.subheader("📊 概览（最近区间）")
        total_messages = overall.get("total_messages", 0)
        total_active_users = overall.get("total_active_users", 0)
        avg_per_user = round(total_messages / total_active_users, 2) if total_active_users > 0 else 0
        peak_daily_active = int(daily_df["active_users"].max()) if not daily_df.empty else 0
        peak_daily_messages = int(daily_df["messages"].max()) if not daily_df.empty else 0

        c1, c2, c3, c4 = st.columns(4)
        with c1:
            st.metric("总消息数", f"{total_messages:,}")
        with c2:
            st.metric("活跃用户数", f"{total_active_users}")
        with c3:
            st.metric("人均消息", f"{avg_per_user}")
        with c4:
            st.metric("峰值(活跃/消息)", f"{peak_daily_active}/{peak_daily_messages}")

        # Daily trends
        st.subheader("📈 最近7天趋势")
        if daily_df.empty:
            st.info("暂无最近区间数据")
        else:
            lcol, rcol = st.columns(2)
            with lcol:
                fig_msg = px.line(daily_df, x="date", y="messages", markers=True, title="每日消息数")
                fig_msg.update_layout(xaxis_title="日期", yaxis_title="消息数", height=350)
                st.plotly_chart(fig_msg, use_container_width=True)
            with rcol:
                fig_user = px.line(daily_df, x="date", y="active_users", markers=True, title="每日活跃用户数")
                fig_user.update_layout(xaxis_title="日期", yaxis_title="活跃用户数", height=350)
                st.plotly_chart(fig_user, use_container_width=True)

            # Show data table
            st.dataframe(daily_df.rename(columns={"date": "日期", "messages": "消息数", "active_users": "活跃用户数"}), use_container_width=True, hide_index=True)

        # Platform stats
        st.subheader("🖥️ 平台分布")
        if platform_df.empty:
            st.info("暂无平台统计数据")
        else:
            p1, p2 = st.columns([2, 1])
            with p1:
                fig_bar = px.bar(platform_df, x="platform", y="messages", title="各平台消息数", text_auto=True)
                fig_bar.update_layout(xaxis_title="平台", yaxis_title="消息数", height=380)
                st.plotly_chart(fig_bar, use_container_width=True)
            with p2:
                pie_df = platform_df.nlargest(8, "messages")
                fig_pie = px.pie(pie_df, values="messages", names="platform", title="平台消息占比(Top 8)")
                fig_pie.update_traces(textposition='inside', textinfo='percent+label')
                st.plotly_chart(fig_pie, use_container_width=True)

            st.dataframe(platform_df.rename(columns={"platform": "平台", "messages": "消息数", "active_users": "活跃用户数"}), use_container_width=True, hide_index=True)

        # Top users
        st.subheader("🏅 高活跃用户")
        if top_users_df.empty:
            st.info("暂无用户活跃数据")
        else:
            # enrich with user info
            user_info_map = _fetch_user_info_map(session, top_users_df["user_id"].astype(int).tolist())
            top_users_df["用户名"] = top_users_df["user_id"].map(lambda uid: user_info_map.get(int(uid), {}).get("name", f"用户{uid}"))
            top_users_df["手机号"] = top_users_df["user_id"].map(lambda uid: user_info_map.get(int(uid), {}).get("phone", "-"))
            display_df = top_users_df.rename(columns={"user_id": "用户ID", "messages": "消息数"})[["用户ID", "用户名", "手机号", "消息数"]]

            ucol1, ucol2 = st.columns([2, 1])
            with ucol1:
                st.dataframe(display_df, use_container_width=True, hide_index=True)
            with ucol2:
                fig_top = px.bar(top_users_df.sort_values("messages", ascending=True).tail(10), x="messages", y="用户名", orientation='h', title="Top 10 用户消息数")
                fig_top.update_layout(height=420, xaxis_title="消息数", yaxis_title="用户")
                st.plotly_chart(fig_top, use_container_width=True) 
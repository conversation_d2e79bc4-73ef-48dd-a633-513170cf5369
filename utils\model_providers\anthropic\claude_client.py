from anthropic import Anthropic, AsyncAnthropic
from anthropic._base_client import BaseClient
import time
import traceback
import httpx
from utils.logger import logger


def should_retry(response: httpx.Response) -> bool:
    """自定义重试逻辑"""
    try:
        result = response.json() if response else {}
        err = result.get("error", {}) if result else {}
        logger.warning(
            f"Claude请求失败, http code {response.status_code}: {result}")
        retry = False

        # 检查错误类型
        if err:
            err_type = err.get("type")
            logger.warning(f"检测到Claude错误类型: {err_type}")

            # 处理过载错误
            if err_type == "overloaded_error":
                retry = True
                logger.warning("检测到overloaded_error错误，将进行重试")

            # 处理速率限制错误
            elif err_type == "rate_limit_error":
                retry = True
                logger.warning("检测到rate_limit_error错误，将进行重试")

            # 处理其他可能需要重试的错误
            elif err_type in ["server_error", "timeout_error"]:
                retry = True
                logger.warning(f"检测到{err_type}错误，将进行重试")

        # 检查HTTP状态码
        elif response.status_code >= 500:
            retry = True
            logger.warning(f"检测到服务器错误状态码: {response.status_code}，将进行重试")

        if retry:
            logger.warning(
                f"请求Claude检测到错误，尝试重试，http code {response.status_code}: {result}")

        return retry
    except Exception as e:
        logger.error(f"should_retry函数执行出错: {str(e)}\n{traceback.format_exc()}")
        # 发生异常时返回True以尝试重试
        return True


class ExtendedAnthropicClient(Anthropic):
    """
    扩展的Anthropic客户端，重写了_should_retry方法以添加自定义重试逻辑
    """

    def _should_retry(self, response: httpx.Response) -> bool:
        # 记录调用信息
        logger.info("ExtendedAnthropicClient._should_retry被调用")

        # 首先调用父类的_should_retry方法
        result = super()._should_retry(response=response)
        logger.info(f"父类_should_retry返回: {result}")

        # 如果父类决定不重试，我们可以添加自己的重试逻辑
        if not result:
            custom_retry = should_retry(response=response)
            logger.info(f"自定义should_retry返回: {custom_retry}")
            return custom_retry

        return result


class ExtendedAsyncAnthropicClient(AsyncAnthropic):
    """
    扩展的AsyncAnthropic客户端，重写了_should_retry方法以添加自定义重试逻辑
    """

    def _should_retry(self, response: httpx.Response) -> bool:
        # 记录调用信息
        logger.info("ExtendedAsyncAnthropicClient._should_retry被调用")

        # 首先调用父类的_should_retry方法
        result = super()._should_retry(response=response)
        logger.info(f"父类_should_retry返回: {result}")

        # 如果父类决定不重试，我们可以添加自己的重试逻辑
        if not result:
            custom_retry = should_retry(response=response)
            logger.info(f"自定义should_retry返回: {custom_retry}")
            return custom_retry

        return result

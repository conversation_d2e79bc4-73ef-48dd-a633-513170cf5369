import streamlit as st
import pandas as pd
from sqlalchemy import text
from utils.mysql import get_sync_mysql
from datetime import datetime
import markdown  # 添加markdown库用于渲染Markdown内容

# 用户聊天记录管理功能


def render_user_management():
    # st.header("用户聊天记录管理")

    # 获取数据库连接
    db_session = get_sync_mysql()

    # 初始化会话状态
    if "selected_user_id" not in st.session_state:
        st.session_state.selected_user_id = None
    if "selected_user_name" not in st.session_state:
        st.session_state.selected_user_name = None
    if "page_number" not in st.session_state:
        st.session_state.page_number = 1
    if "page_size" not in st.session_state:
        st.session_state.page_size = 10
    if "user_remark" not in st.session_state:
        st.session_state.user_remark = ""

    # 加载统计数据
    # overall_stats = get_overall_stats(db_session)

    # # 显示总体统计信息
    # col1, col2, col3, col4, col5 = st.columns(5)
    # with col1:
    #     st.metric("活跃用户数", f"{overall_stats['total_active_users']}")
    # with col2:
    #     st.metric("总消息数", f"{overall_stats['total_messages']}")

    # 创建两列布局
    left_col, right_col = st.columns([1, 2])

    # 左侧显示用户列表
    with left_col:
        st.subheader("用户列表")

        # 添加搜索框和刷新按钮
        col1, col2 = st.columns([3, 1])
        with col1:
            search_query = st.text_input("搜索用户名或手机号", key="user_search")

        with db_session() as session:
            # 加载用户数据
            users_df = load_users(session)

        # 应用搜索过滤
        if search_query:
            users_df = users_df[
                users_df["name"].str.contains(search_query, na=False, case=False) |
                users_df["phone"].str.contains(
                    search_query, na=False, case=False)
            ]

        # 显示用户列表
        if not users_df.empty:
            # 格式化最后聊天时间
            users_df["last_chat_time"] = users_df["last_chat_time"].apply(
                lambda x: x.strftime("%Y-%m-%d %H:%M") if pd.notna(x) else "-"
            )

            # 分页设置
            total_users = len(users_df)
            total_pages = (total_users + st.session_state.page_size -
                           1) // st.session_state.page_size

            # 计算当前页的用户
            start_idx = (st.session_state.page_number - 1) * \
                st.session_state.page_size
            end_idx = min(start_idx + st.session_state.page_size, total_users)
            current_page_users = users_df.iloc[start_idx:end_idx]

            # 添加CSS样式
            st.markdown("""
            <style>
            .user-item {
                padding: 10px 15px;
                border-bottom: 1px solid #333;
                margin-bottom: 5px;
                background-color: #1e1e1e;
                transition: background-color 0.2s;
            }
            .user-item:hover {
                background-color: #2d2d2d;
            }
            .user-name {
                font-weight: bold;
                display: inline-block;
                margin-right: 10px;
            }
            .user-phone {
                color: #a0aec0;
                font-size: 12px;
                display: inline-block;
                margin-right: 10px;
            }
            .user-remark {
                color: #f59e0b;
                font-size: 12px;
                display: block;
                margin-top: 5px;
            }
            .user-stats {
                display: inline-block;
                font-size: 12px;
                color: #a0aec0;
            }
            .user-stats span {
                margin-right: 10px;
            }
            .selected {
                border-left: 3px solid #4CAF50;
                background-color: #2d2d2d;
            }
            /* 自定义按钮样式 */
            .stButton > button {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 3px 6px;
                font-size: 9px;
                cursor: pointer;
                line-height: 1;
            }
            .stButton > button:hover {
                background-color: #45a049;
            }                                     
            </style>
            """, unsafe_allow_html=True)

            # 显示用户列表
            for i, user in enumerate(current_page_users.itertuples()):
                # 创建一个列容器，分为用户信息和按钮两部分
                col1, col2 = st.columns([5, 1])

                with col1:
                    # 检查是否为选中的用户
                    is_selected = st.session_state.selected_user_id == user.id
                    selected_class = "selected" if is_selected else ""

                    # 创建用户信息区域
                    st.markdown(f"""
                    <div class="user-item {selected_class}">
                        <div class="user-name">{user.id}</div>
                        <div class="user-name">{user.name}</div>
                        <div class="user-phone">{user.phone if user.phone else "-"}</div>
                        <div class="user-stats">
                            <span>消息: {user.message_count}</span>
                            <span>最近: {user.last_chat_time}</span>
                        </div>
                        <div class="user-remark">{user.remark if user.remark else ""}</div>
                    </div>
                    """, unsafe_allow_html=True)

                with col2:
                    # 使用Streamlit原生按钮
                    if st.button("查看", key=f"view_chat_{user.id}", type="primary"):
                        st.session_state.selected_user_id = user.id
                        st.session_state.selected_user_name = user.name
                        st.session_state.user_remark = user.remark if pd.notna(
                            user.remark) else ""
                        st.rerun()

            # 分页导航 - 移动到用户列表下面
            st.write(f"共 {total_users} 位用户")

            # 计算要显示的页码范围
            max_visible_pages = 5  # 最多显示的页码数
            half_visible = max_visible_pages // 2

            if total_pages <= max_visible_pages:
                # 如果总页数少于最大可见页码数，显示所有页码
                start_page = 1
                end_page = total_pages
            else:
                # 否则，显示当前页附近的页码
                start_page = max(
                    1, st.session_state.page_number - half_visible)
                end_page = min(total_pages, start_page + max_visible_pages - 1)

                # 调整起始页，确保显示足够的页码
                if end_page - start_page + 1 < max_visible_pages:
                    start_page = max(1, end_page - max_visible_pages + 1)

            # 创建分页导航 - 使用单层列布局
            pagination_cols = st.columns(
                [1] + [1] * min(max_visible_pages, end_page - start_page + 1) + [1])

            # 上一页按钮
            with pagination_cols[0]:
                if st.button("上页", disabled=(st.session_state.page_number <= 1), use_container_width=True, key="prev_page"):
                    st.session_state.page_number -= 1
                    st.rerun()

            # 分页按钮代码
            for i, page in enumerate(range(start_page, end_page + 1)):
                with pagination_cols[i + 1]:
                    button_type = "primary" if page == st.session_state.page_number else "secondary"
                    if st.button(f"{page}", key=f"page_{page}", use_container_width=True, type=button_type):
                        st.session_state.page_number = page
                        st.rerun()

            # 下一页按钮
            with pagination_cols[-1]:
                if st.button("下页", disabled=(st.session_state.page_number >= total_pages), use_container_width=True, key="next_page"):
                    st.session_state.page_number += 1
                    st.rerun()

            # 显示当前页码信息
            st.caption(f"第 {st.session_state.page_number}/{total_pages} 页")

        else:
            st.info("没有找到用户数据")

    # 右侧显示聊天记录
    with right_col:
        if st.session_state.selected_user_id is not None:
            # 添加用户备注编辑区
            with st.expander("编辑用户备注", expanded=False):
                remark = st.text_area(
                    "用户备注", value=st.session_state.user_remark, height=100)

                if st.button("保存备注"):

                    with db_session() as session:
                        save_user_remark(
                            session, st.session_state.selected_user_id, remark)
                    st.session_state.user_remark = remark
                    st.success("备注已更新")

            st.subheader(f"{st.session_state.selected_user_name} 的聊天记录")

            # 添加筛选选项
            filter_option = st.selectbox(
                "筛选消息",
                ["全部消息", "仅点赞消息", "仅点踩消息", "无反馈消息"],
                key="message_filter"
            )

            # 获取聊天记录
            with db_session() as session:
                chats_df = get_user_chats(
                    session, st.session_state.selected_user_id)

            # 根据筛选条件过滤消息
            if not chats_df.empty and filter_option != "全部消息":
                if filter_option == "仅点赞消息":
                    chats_df = chats_df[(chats_df["is_robot"] == 1) & (chats_df["is_like"] == 1)]
                elif filter_option == "仅点踩消息":
                    chats_df = chats_df[(chats_df["is_robot"] == 1) & (chats_df["is_dislike"] == 1)]
                elif filter_option == "无反馈消息":
                    chats_df = chats_df[(chats_df["is_robot"] == 1) & (chats_df["is_like"] == 0) & (chats_df["is_dislike"] == 0)]

            # 显示聊天记录
            if not chats_df.empty:
                # 创建一个完整的HTML字符串，包含所有消息和滚动容器
                full_html = """
                <!DOCTYPE html>
                <html>
                <head>
                <style>
                body {
                    margin: 0;
                    padding: 0;
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                }
                .chat-scroll-container {
                     height: 900px;
                    overflow-y: auto;
                    border: 1px solid #4a5568;
                    border-radius: 5px;
                    background-color: #1e293b;
                    padding: 10px;
                    margin-bottom: 20px;
                }
                .chat-container {
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 15px;
                    width: 100%;
                }
                .message {
                    padding: 12px 15px;
                    margin: 5px 0;
                    border-radius: 10px;
                    max-width: 80%;
                    word-wrap: break-word;
                    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                }
                .user-message {
                    background-color: #3b4a63;
                    color: white;
                    margin-left: auto;
                    margin-right: 0;
                    border-bottom-right-radius: 0;
                    border-right: 3px solid #10b981;
                    text-align: right;
                }
                .ai-message {
                    background-color: #1e3a8a;
                    color: white;
                    margin-right: auto;
                    margin-left: 0;
                    border-bottom-left-radius: 0;
                    border-left: 3px solid #3b82f6;
                    text-align: left;
                }
                .message-time {
                    font-size: 12px;
                    color: #cbd5e0;
                    margin-top: 5px;
                    text-align: right;
                }
                .message-feedback {
                    font-size: 11px;
                    margin-top: 8px;
                    padding-top: 5px;
                    border-top: 1px solid rgba(255,255,255,0.1);
                }
                .feedback-like {
                    color: #10b981;
                    display: inline-block;
                    margin-right: 15px;
                }
                .feedback-dislike {
                    color: #ef4444;
                    display: inline-block;
                }
                .dislike-reason {
                    color: #fbbf24;
                    font-style: italic;
                    margin-left: 5px;
                }
                .message-content {
                    line-height: 1.6;
                }
                .user-container {
                    display: flex;
                    justify-content: flex-end;
                    width: 100%;
                }
                .ai-container {
                    display: flex;
                    justify-content: flex-start;
                    width: 100%;
                }
                /* Markdown样式 */
                .message-content h1, .message-content h2, .message-content h3, 
                .message-content h4, .message-content h5, .message-content h6 {
                    margin-top: 0.5em;
                    margin-bottom: 0.5em;
                    font-weight: bold;
                }
                .message-content h1 { font-size: 1.8em; }
                .message-content h2 { font-size: 1.6em; }
                .message-content h3 { font-size: 1.4em; }
                .message-content h4 { font-size: 1.2em; }
                .message-content h5 { font-size: 1em; }
                .message-content h6 { font-size: 0.9em; }
                .message-content p {
                    margin: 0.5em 0;
                }
                .message-content ul, .message-content ol {
                    margin-left: 1.5em;
                    padding-left: 0;
                }
                .message-content li {
                    margin: 0.25em 0;
                }
                .message-content code {
                    font-family: monospace;
                    background-color: rgba(0,0,0,0.3);
                    padding: 2px 4px;
                    border-radius: 3px;
                }
                .message-content pre {
                    background-color: rgba(0,0,0,0.3);
                    padding: 8px;
                    border-radius: 5px;
                    overflow-x: auto;
                }
                .message-content blockquote {
                    border-left: 3px solid #a0aec0;
                    padding-left: 10px;
                    margin-left: 0;
                    color: #cbd5e0;
                }
                .message-content table {
                    border-collapse: collapse;
                    width: 100%;
                    margin: 1em 0;
                }
                .message-content th, .message-content td {
                    border: 1px solid #4a5568;
                    padding: 6px;
                    text-align: left;
                }
                .message-content th {
                    background-color: rgba(0,0,0,0.2);
                }
                .message-content a {
                    color: #63b3ed;
                    text-decoration: none;
                }
                .message-content a:hover {
                    text-decoration: underline;
                }
                .message-content img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 5px;
                }
                </style>
                </head>
                <body>
                <div class="chat-scroll-container">
                """

                # 添加所有消息到HTML字符串
                for _, chat in chats_df.iterrows():
                    # 确定消息类型和样式
                    is_ai = chat["is_robot"] == 1
                    message_type = "ai-message" if is_ai else "user-message"
                    container_type = "ai-container" if is_ai else "user-container"

                    # 将Markdown内容转换为HTML
                    try:
                        import json
                        # 检查内容是否为JSON格式
                        if chat["content"].strip().startswith('[') and chat["content"].strip().endswith(']'):
                            try:
                                json_data = json.loads(chat["content"])
                                parsed_content = []

                                for item in json_data:
                                    role = item.get("role", "")

                                    if role == "assistant" and "content" in item:
                                        parsed_content.append(
                                            f"------------------\n\n {item['content']}")
                                    elif role == "tool" and "content" in item:
                                        tool_content = item["content"]
                                        tool_name = tool_content.get("tool_name", "未知工具")
                                        tool_args = tool_content.get("tool_args", {})
                                        tool_call_id = tool_content.get("tool_call_id", "")
                                        tool_result = tool_content.get("tool_result", "")

                                        # 确保tool_args不为None
                                        if tool_args is None:
                                            tool_args = {}

                                        # 处理tool_args可能是列表或字典的情况
                                        if isinstance(tool_args, dict):
                                            args_str = ", ".join(
                                                [f"{k}: {v}" for k, v in tool_args.items()])
                                        elif isinstance(tool_args, list):
                                            args_str = ", ".join(
                                                [str(item) for item in tool_args])
                                        else:
                                            args_str = str(tool_args)

                                        parsed_content.append(
                                            f"**工具调用**: {tool_name}\n**参数**: {args_str}\n**结果**: {json.dumps(tool_result, ensure_ascii=False)}")
                                    else:
                                        parsed_content.append(
                                            f"**{role}**: {str(item.get('content', ''))}")

                                # 将解析后的内容合并为Markdown格式
                                message_content = markdown.markdown("\n\n".join(
                                    parsed_content), extensions=['extra', 'codehilite'])
                            except json.JSONDecodeError:
                                # 如果JSON解析失败，按普通Markdown处理
                                message_content = markdown.markdown(
                                    chat["content"], extensions=['extra', 'codehilite'])
                        else:
                            # 不是JSON格式，按普通Markdown处理
                            message_content = markdown.markdown(
                                chat["content"], extensions=['extra', 'codehilite'])
                    except Exception as e:
                        # 处理任何异常，确保显示原始内容
                        st.error(f"消息内容解析错误: {e}")
                        message_content = markdown.markdown(
                            chat["content"], extensions=['extra', 'codehilite'])

                    # 构建反馈状态HTML
                    feedback_html = ""
                    if chat["is_robot"] == 1:  # 只有AI消息才显示反馈状态
                        feedback_parts = []
                        if chat["is_like"] == 1:
                            feedback_parts.append('<span class="feedback-like">👍 用户点赞</span>')
                        if chat["is_dislike"] == 1:
                            dislike_text = '<span class="feedback-dislike">👎 用户点踩</span>'
                            if chat["dislike_reason"] and chat["dislike_reason"].strip():
                                dislike_text += f'<span class="dislike-reason">原因: {chat["dislike_reason"]}</span>'
                            feedback_parts.append(dislike_text)

                        if feedback_parts:
                            feedback_html = f'<div class="message-feedback">{"".join(feedback_parts)}</div>'

                    # 添加消息到HTML
                    full_html += f"""
                    <div class="{container_type}">
                        <div class="message {message_type}">
                            <div class="message-content">{message_content}</div>
                            <div class="message-time">{chat["created_at"].strftime('%Y-%m-%d %H:%M')}</div>
                            {feedback_html}
                        </div>
                    </div>
                    """

                # 关闭滚动容器和HTML标签
                full_html += """
                </div>
                <script>
                // 页面加载完成后自动滚动到底部
                document.addEventListener('DOMContentLoaded', function() {
                    var chatContainer = document.querySelector('.chat-scroll-container');
                    if (chatContainer) {
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    }
                });
                
                // 备用方法：如果DOMContentLoaded事件已经触发，立即执行
                setTimeout(function() {
                    var chatContainer = document.querySelector('.chat-scroll-container');
                    if (chatContainer) {
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    }
                }, 100);
                </script>
                </body>
                </html>
                """

                # 一次性显示完整的HTML
                st.components.v1.html(full_html, height=920, scrolling=False)
            else:
                st.info("该用户暂无聊天记录")
        else:
            st.info("请从左侧选择一个用户查看聊天记录")


# 保存用户备注
def save_user_remark(db_session, user_id, remark):
    try:
        query = text("""
            UPDATE user
            SET ad_remark = :remark
            WHERE id = :user_id
        """)

        db_session.execute(query, {
            "user_id": user_id,
            "remark": remark
        })

        db_session.commit()
        return True
    except Exception as e:
        st.error(f"保存用户备注失败: {e}")
        db_session.rollback()
        return False

# 加载用户数据


def load_users(db_session):
    try:
        # 查询用户列表，包括每个用户的消息数量和最近聊天时间
        query = text("""
            SELECT 
                u.id, 
                u.name, 
                u.mobile, 
                u.ad_remark as remark,
                COUNT(CASE WHEN uc.is_robot = 0 THEN uc.id ELSE NULL END) as message_count, 
                MAX(CASE WHEN uc.is_robot = 0 THEN uc.created_at ELSE NULL END) as last_chat_time
            FROM 
                user u
            LEFT JOIN 
                user_chat uc ON u.id = uc.user_id
            GROUP BY 
                u.id, u.name, u.mobile, u.ad_remark
            ORDER BY 
                CASE WHEN last_chat_time IS NULL THEN 0 ELSE 1 END DESC,
                last_chat_time DESC, 
                u.id ASC
        """)

        result = db_session.execute(query)
        users_data = []
        for row in result:
            users_data.append({
                "id": row.id,
                "name": row.name,
                "phone": row.mobile,
                "remark": row.remark,
                "message_count": row.message_count if row.message_count else 0,
                "last_chat_time": row.last_chat_time
            })

        return pd.DataFrame(users_data)
    except Exception as e:
        st.error(f"加载用户数据失败: {e}")
        return pd.DataFrame(columns=["id", "name", "phone", "remark", "message_count", "last_chat_time"])

# 获取用户聊天记录


def get_user_chats(db_session, user_id):
    try:
        query = text("""
            SELECT
                uc.id,
                uc.content,
                uc.is_robot,
                uc.is_like,
                uc.is_dislike,
                uc.dislike_reason,
                uc.created_at
            FROM
                user_chat uc
            WHERE
                uc.user_id = :user_id

            ORDER BY
                uc.id DESC
            limit 200
        """)

        result = db_session.execute(query, {
            "user_id": user_id
        })

        chats_data = []
        for row in result:
            chats_data.append({
                "id": row.id,
                "content": row.content,
                "is_robot": row.is_robot,
                "is_like": row.is_like,
                "is_dislike": row.is_dislike,
                "dislike_reason": row.dislike_reason,
                "created_at": row.created_at
            })

        # 创建DataFrame并反转顺序，使其按时间正序排列
        df = pd.DataFrame(chats_data)
        if not df.empty:
            df = df.iloc[::-1].reset_index(drop=True)

        return df
    except Exception as e:
        st.error(f"加载聊天记录失败: {e}")
        return pd.DataFrame(columns=["id", "content", "is_robot", "is_like", "is_dislike", "dislike_reason", "created_at"])

# 获取总体统计数据


def get_overall_stats(db_session):
    try:
        query = text("""
            SELECT 
                COUNT(DISTINCT uc.user_id) as total_active_users,
                COUNT(uc.id) as total_messages
            FROM 
                user_chat uc
        """)

        result = db_session.execute(query)
        row = result.fetchone()

        return {
            "total_active_users": row.total_active_users if row else 0,
            "total_messages": row.total_messages if row else 0,
        }
    except Exception as e:
        st.error(f"加载总体统计数据失败: {e}")
        return {
            "total_active_users": 0,
            "total_messages": 0,
        }

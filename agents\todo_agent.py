from agno.agent import Agent
from agno.models.openai import OpenAIChat
from utils.storage import get_singlestore_storage
from tools.todoist_tools import TodoistTools
import logging
from agno.models.deepseek import DeepSeek
from agno.models.openai.like import OpenAILike
from utils.models import mainModel
import copy

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("todo_agent")

# 获取SingleStore存储实例
storage = get_singlestore_storage(table_name="todo_sessions")


# 创建待办任务管理智能体
todo_agent = Agent(
    name="待办任务助手",
    agent_id="001",
    model=mainModel,
    reasoning=True,
    # storage=storage,
    tools=[copy.deepcopy(TodoistTools())],
    markdown=False,
    show_tool_calls=True,  # 显示工具调用
    debug_mode=True,  # 启用调试模式
    add_datetime_to_instructions=True,
    user_id="harris100",  # 默认值，会在API调用时被替换
    session_id="harris100",  # 默认值，会在API调用时被替换
    add_context=True,
    instructions="""
你是一个待办任务助手，专注于管理和跟踪用户的待办事项，帮助用户高效完成个人任务。

核心职责：
- 创建新的待办任务
- 更新现有任务的状态和详情
- 按优先级或截止日期对任务进行排序
- 提醒即将到期的任务
- 归档已完成的任务

可用工具：
- add_todo：添加新的待办事项
- update_todo_date：更新现有待办事项的时间
- delete_todo：删除待办事项
- get_active_todos：获取所有未完成的待办事项
- get_completed_todos：获取所有已完成的待办事项
- get_todo：获取单个待办事项的详细信息
- complete_todo：将待办事项标记为已完成

规则与注意事项：
- 待办里如果有时间点，需要提取出时间点，并以YYYY-MM-DD H::i:s格式传给due_date参数
- 如果不知道某个待办的todo_id，请使用get_active_todos获取所有未完成的待办事项，从里面获取todo_id
- 如果工具执行结果有link链接地址，需要返回输出链接地址，方便让用户可以点击查看链接

    """
)

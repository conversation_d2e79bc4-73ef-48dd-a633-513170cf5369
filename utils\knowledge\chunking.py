from typing import List

from agno.document.base import Document
from agno.document.chunking.document import DocumentChunking

class DocumentChunking(DocumentChunking):
    def __init__(self, chunk_size: int = 5000, overlap: int = 0):
        super().__init__(chunk_size=chunk_size, overlap=overlap)
        self.chunk_size = chunk_size
        self.overlap = overlap

    def chunk(self, document: Document) -> List[Document]:
        """Split document into chunks based on document structure"""
        documents = super().chunk(document)
        return documents
    
    def clean_text(self, text: str) -> str:
        """清理换行和空格后会导致无法按段落分块, 所以覆写了这个方法不做任何处理"""
        return text
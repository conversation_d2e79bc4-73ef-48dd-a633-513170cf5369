from agno.agent import Agent
from agno.media import Image
from agno.models.openai import OpenAIChat
from typing import Optional, List
import logging
from pathlib import Path
import requests
import io

# 配置日志
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ImageAgent")

class ImageAgent:
    """图片解析Agent，用于解析图片内容并生成描述"""

    def __init__(self, api_key: Optional[str] = None):
        """
        初始化图片解析Agent
        
        Args:
            api_key: OpenAI API密钥，如果为None则从环境变量获取
        """
        self.agent = Agent(
            model=OpenAIChat(id="gpt-4-vision-preview"),
            agent_id="image-analyzer",
            name="Image Analysis Agent",
            markdown=True,
            debug_mode=True,
            show_tool_calls=True,
            instructions=[
                "你是一个专业的图片分析AI助手，可以详细描述图片内容。",
                "你需要用中文回答，并尽可能详细地描述图片中的内容。",
                "如果图片中包含文字，请准确识别并描述。",
                "如果图片中包含图表或数据，请分析并总结关键信息。",
                "如果图片中包含人物，请描述他们的表情、动作和场景。",
                "如果图片中包含物体，请描述它们的特征和状态。",
                "请用清晰、专业的语言描述，避免使用过于口语化的表达。"
            ],
        )
        logger.info("ImageAgent初始化完成")

    def analyze_image(self, image_path: str, prompt: str = "请详细描述这张图片的内容") -> str:
        """
        分析图片内容
        
        Args:
            image_path: 图片路径或URL
            prompt: 分析提示词
            
        Returns:
            str: 图片内容描述
        """
        try:
            # 处理图片路径
            if image_path.startswith(('http://', 'https://')):
                # 如果是URL，下载图片
                response = requests.get(image_path)
                image_data = Image(data=response.content)
            else:
                # 如果是本地路径
                image_path = Path(image_path)
                if not image_path.exists():
                    raise FileNotFoundError(f"图片文件不存在: {image_path}")
                image_data = Image(filepath=image_path)

            # 调用agent分析图片
            response = self.agent.run(
                prompt,
                images=[image_data],
                stream=False
            )
            
            logger.info("图片分析完成")
            return response

        except Exception as e:
            logger.error(f"图片分析失败: {str(e)}")
            return f"图片分析失败: {str(e)}"

    def analyze_multiple_images(self, image_paths: List[str], prompt: str = "请详细描述这些图片的内容") -> str:
        """
        分析多张图片内容
        
        Args:
            image_paths: 图片路径或URL列表
            prompt: 分析提示词
            
        Returns:
            str: 图片内容描述
        """
        try:
            images = []
            for path in image_paths:
                if path.startswith(('http://', 'https://')):
                    response = requests.get(path)
                    images.append(Image(data=response.content))
                else:
                    image_path = Path(path)
                    if not image_path.exists():
                        raise FileNotFoundError(f"图片文件不存在: {path}")
                    images.append(Image(filepath=image_path))

            # 调用agent分析多张图片
            response = self.agent.run(
                prompt,
                images=images,
                stream=False
            )
            
            logger.info("多张图片分析完成")
            return response

        except Exception as e:
            logger.error(f"多张图片分析失败: {str(e)}")
            return f"多张图片分析失败: {str(e)}"

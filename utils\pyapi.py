import aiohttp
import json
import requests

from utils.context import get_context
from utils.logger import logger
import os
from dotenv import load_dotenv
load_dotenv()

pyapi_domain = os.getenv("API_URL")
rsp_log_len = 100

def call_rpc(class_name: str, method_name: str, params: dict, timeout: int = 60) -> str:
    """调用RPC方法"""
    try:
        data = {"class": class_name, "method": method_name, "params": params}
        logger.info(f"调用RPC方法: {data}")
        response = requests.post(f"{pyapi_domain}/pyapi/rpc", json=data, timeout=timeout)
        logger.info(f"调用RPC方法({class_name}.{method_name})返回: {response.text[:rsp_log_len]}")        
        if response.status_code == 200:
            result = response.json()
            if result.get("code", -1) == 0:
                return response.text
        err = f"status_code={response.status_code}, {response.text[:rsp_log_len]}"
    except Exception as e:
        err = str(e)
    logger.error(f"调用RPC方法失败, data={data}, err: {err}")
    return json.dumps({"error": "调用RPC方法失败."}, ensure_ascii=False)

async def async_call_rpc(class_name: str, method_name: str, params: dict, timeout: int = 60) -> str:
    """异步调用RPC方法"""
    try:
        data = {"class": class_name, "method": method_name, "params": params}
        logger.info(f"异步调用RPC方法: {data}")        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{pyapi_domain}/pyapi/rpc", json=data, timeout=timeout) as response:
                rsp_text = await response.text()
                logger.info(f"异步调用RPC方法({class_name}.{method_name})返回: {rsp_text[:rsp_log_len]}")                
                if response.status == 200:
                    result = await response.json()
                    if result.get("code", -1) == 0:
                        return rsp_text
                err = f"status_code={response.status}, {rsp_text[:rsp_log_len]}"                
    except Exception as e:
        err = str(e)
    logger.error(f"异步调用RPC方法失败, data={data}, err: {err}")    
    return json.dumps({"error": "异步调用RPC方法失败."}, ensure_ascii=False)

def call_api(route: str, data: dict = {}, method: str = "POST", timeout: int = 60) -> str:
    """调用API方法"""
    try:
        user_id = get_context("user_id")
        req_info = f"{method} {route}, user_id={user_id}, data={data}"
        logger.info(req_info)
        response = requests.request(method=method, url=f"{pyapi_domain}{route}", params={"user_id": user_id}, json=data, timeout=timeout)
        logger.info(f"调用API方法({route})返回: {response.text[:rsp_log_len]}")        
        if response.status_code == 200:
            result = response.json()
            if result.get("code", -1) == 0:
                return response.text
        err = f"status_code={response.status_code}, {response.text[:rsp_log_len]}"
    except Exception as e:
        err = str(e)
    logger.error(f"调用API方法失败, {req_info}, err: {err}")
    return json.dumps({"error": "调用API方法失败."}, ensure_ascii=False)
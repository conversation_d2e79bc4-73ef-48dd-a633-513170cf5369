from agno.agent import Agent
from agno.models.openai import OpenAIChat

from agno.models.openai.like import OpenAILike
from agno.tools.tavily import TavilyTools


modelQwen = OpenAILike(
    id="qwen-max",
    api_key="sk-1900eb026aee4b95992186f796fdde08",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


# 创建网络搜索智能体
search_agent = Agent(
    name="网络搜索助手",
    model=modelQwen,
    tools=[TavilyTools()],
    show_tool_calls=True,
    markdown=True,
    instructions="""
    你是一个专业的网络搜索助手。你可以帮助用户:
    - 搜索特定公司和客户的信息
    - 查找行业趋势和新闻
    - 收集竞争对手分析
    - 查找潜在客户的详细背景
    - 搜索特定技术或产品信息
    
    请提供可靠且最新的信息，并引用来源。
    """
) 
from typing import Any, List, Literal, Optional

from agno.storage.base import Storage
from sqlalchemy.engine import Engine
from agno.storage.singlestore import SingleStoreStorage
from agno.storage.session import Session
from utils.context import get_context

num_history_runs = 10  # 主会话保留的历史对话轮数
bs_num_history_runs = 30  # 商机会话保留的历史对话轮数
preserve_recent_runs = 3  # 保留的最近几轮不过滤(工具调用)


class SingleStoreStorage(SingleStoreStorage):
    def __init__(
        self,
        table_name: str,
        schema: Optional[str] = "ai",
        db_url: Optional[str] = None,
        db_engine: Optional[Engine] = None,
        schema_version: int = 1,
        auto_upgrade_schema: bool = False,
        mode: Optional[Literal["agent", "team", "workflow"]] = "agent",
    ):
        """
        This class provides Agent storage using a singlestore table.

        The following order is used to determine the database connection:
            1. Use the db_engine if provided
            2. Use the db_url if provided

        Args:
            table_name (str): The name of the table to store the agent data.
            schema (Optional[str], optional): The schema of the table. Defaults to "ai".
            db_url (Optional[str], optional): The database URL. Defaults to None.
            db_engine (Optional[Engine], optional): The database engine. Defaults to None.
            schema_version (int, optional): The schema version. Defaults to 1.
            auto_upgrade_schema (bool, optional): Automatically upgrade the schema. Defaults to False.
            mode (Optional[Literal["agent", "team", "workflow"]], optional): The mode of the storage. Defaults to "agent".
        """
        super().__init__(table_name=table_name, schema=schema, db_url=db_url, db_engine=db_engine,
                         schema_version=schema_version, auto_upgrade_schema=auto_upgrade_schema, mode=mode)

    def upsert(self, session: Session) -> Optional[Session]:
        """
        覆写官方的upsert方法, 只保留session的最近n条runs
        Create a new session if it does not exist, otherwise update the existing session.

        优化：只对超过preserve_recent_runs轮的历史对话执行过滤，保留最新的preserve_recent_runs轮完整内容
        """
        session_id = get_context("session_id")
        is_business_session = str(session_id).startswith("business-")
        num_runs = bs_num_history_runs if is_business_session else num_history_runs
        # 只保留最近的num_history_runs轮对话
        session.memory["runs"] = session.memory["runs"][-num_runs:]

        # 获取runs的总数
        total_runs = len(session.memory["runs"])
        # 工具结果保留的内容长度
        tool_content_len = 100

        for i, run in enumerate(session.memory["runs"]):

            filtered_messages = []
            for message in run["messages"]:
                role = message.get("role")
                content = message.get("content", "")

                # 处理内容为空的消息并过滤不符合条件的消息
                if "tool_calls" not in message and not content:
                    message["content"] = "."

                # 只对较早的对话轮次执行过滤，保留最近preserve_recent_runs轮的完整工具内容
                if i >= total_runs - preserve_recent_runs:
                    filtered_messages.append(message)
                    continue

                if role == "tool" and len(content) > tool_content_len:
                    message["content"] = content[:tool_content_len] + \
                        "...[该工具调用历史记录完整结果已隐藏, 如需查看完整结果请重新调用]"
#
#                 # 较早的消息只保留最后的回复内容，过滤掉中间的tool调用
#                 if "role" not in message:
#                     continue
#
#                 if message.get("role") == "system":
#                     filtered_messages.append(message)
#                     continue
#
#                 # 条件1: 如果role等于user或assistant, content必须要有值且是字符串
#                 if message.get("role") == "user" or message.get("role") == "assistant":
#                     if isinstance(message.get("content"), str) and message.get("content") and "tool_calls" not in message:
#                         filtered_messages.append(message)
#
#             run["messages"] = filtered_messages

        super().upsert(session)

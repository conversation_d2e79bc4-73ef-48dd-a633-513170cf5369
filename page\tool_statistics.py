import streamlit as st
import pandas as pd
from sqlalchemy import text
from utils.mysql import get_sync_mysql
from utils.redis import get_client
from utils.tool_name_map import tool_name_map
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots


def render_tool_statistics():
    """渲染工具调用统计页面"""
    st.header("🔧 工具调用统计")
    
    # 获取数据库和Redis连接
    db_session = get_sync_mysql()
    redis_client = get_client()
    
    # 获取统计数据
    total_stats = get_total_tool_stats(redis_client)
    user_stats = get_user_tool_stats(redis_client, db_session)
    
    # 显示总体概览
    render_overview(total_stats, user_stats)
    
    # 显示工具调用统计
    render_tool_usage_stats(total_stats)
    
    # 显示用户统计
    render_user_stats(user_stats)


def get_total_tool_stats(redis_client):
    """获取总体工具调用统计"""
    try:
        # 从Redis获取总体统计数据
        total_data = redis_client.hgetall("tool_calls_count:total")
        
        # 转换工具名称为中文并计算统计
        stats = []
        for tool_name, count in total_data.items():
            chinese_name = tool_name_map.get(tool_name, tool_name)
            stats.append({
                "tool_name": tool_name,
                "chinese_name": chinese_name,
                "total_calls": int(count)
            })
        
        # 按调用次数排序
        stats.sort(key=lambda x: x["total_calls"], reverse=True)
        
        return stats
    except Exception as e:
        st.error(f"获取总体工具统计失败: {e}")
        return []


def get_user_tool_stats(redis_client, db_session):
    """获取用户工具调用统计"""
    try:
        # 获取所有用户的工具调用统计
        user_stats = {}
        
        # 获取所有以tool_calls_count:开头的键（排除total）
        keys = redis_client.keys("tool_calls_count:*")
        user_keys = [key for key in keys if key != "tool_calls_count:total"]
        
        for key in user_keys:
            user_id = key.split(":")[-1]
            user_data = redis_client.hgetall(key)
            
            if user_data:  # 只处理有数据的用户
                total_calls = sum(int(count) for count in user_data.values())
                user_stats[user_id] = {
                    "user_id": user_id,
                    "total_calls": total_calls,
                    "tools": user_data
                }
        
        # 获取用户基本信息
        if user_stats:
            with db_session() as session:
                user_ids = list(user_stats.keys())
                user_info = get_user_info(session, user_ids)
                
                # 合并用户信息
                for user_id, info in user_info.items():
                    if user_id in user_stats:
                        user_stats[user_id].update(info)
        
        # 转换为列表并排序
        result = list(user_stats.values())
        result.sort(key=lambda x: x["total_calls"], reverse=True)
        
        return result
    except Exception as e:
        st.error(f"获取用户工具统计失败: {e}")
        return []


def get_user_info(db_session, user_ids):
    """获取用户基本信息"""
    try:
        if not user_ids:
            return {}
            
        # 构建查询
        placeholders = ",".join([f":user_id_{i}" for i in range(len(user_ids))])
        query = text(f"""
            SELECT id, name, mobile
            FROM user 
            WHERE id IN ({placeholders})
        """)
        
        # 构建参数字典
        params = {f"user_id_{i}": user_id for i, user_id in enumerate(user_ids)}
        
        result = db_session.execute(query, params)
        
        user_info = {}
        for row in result:
            user_info[str(row.id)] = {
                "name": row.name or f"用户{row.id}",
                "phone": row.mobile or "-"
            }
        
        return user_info
    except Exception as e:
        st.error(f"获取用户信息失败: {e}")
        return {}


def render_overview(total_stats, user_stats):
    """渲染总体概览"""
    st.subheader("📊 总体概览")
    
    # 计算概览数据
    total_calls = sum(stat["total_calls"] for stat in total_stats)
    active_users = len(user_stats)
    total_tools = len(total_stats)
    most_used_tool = total_stats[0]["chinese_name"] if total_stats else "无"
    
    # 显示指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总调用次数", f"{total_calls:,}")
    
    with col2:
        st.metric("活跃用户数", f"{active_users}")
    
    with col3:
        st.metric("工具总数", f"{total_tools}")
    
    with col4:
        st.metric("最常用工具", most_used_tool)


def render_tool_usage_stats(total_stats):
    """渲染工具使用统计"""
    st.subheader("🛠️ 工具调用统计")
    
    if not total_stats:
        st.info("暂无工具调用数据")
        return
    
    # 创建两列布局
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # 创建DataFrame
        df = pd.DataFrame(total_stats)
        
        # 显示表格
        st.dataframe(
            df[["chinese_name", "total_calls"]].rename(columns={
                "chinese_name": "工具名称",
                "total_calls": "调用次数"
            }),
            use_container_width=True,
            hide_index=True
        )
    
    with col2:
        # 创建饼图
        if len(total_stats) > 0:
            # 取前10个工具
            top_tools = total_stats[:10]
            
            fig = px.pie(
                values=[stat["total_calls"] for stat in top_tools],
                names=[stat["chinese_name"] for stat in top_tools],
                title="工具调用分布（Top 10）"
            )
            fig.update_traces(textposition='inside', textinfo='percent+label')
            st.plotly_chart(fig, use_container_width=True)


def render_user_stats(user_stats):
    """渲染用户统计"""
    st.subheader("👥 用户工具调用统计")
    
    if not user_stats:
        st.info("暂无用户统计数据")
        return
    
    # 搜索框
    search_query = st.text_input("搜索用户（姓名或手机号）", key="user_stats_search")
    
    # 过滤用户数据
    filtered_users = user_stats
    if search_query:
        filtered_users = [
            user for user in user_stats
            if search_query.lower() in user.get("name", "").lower() or
               search_query in user.get("phone", "")
        ]
    
    # 显示用户列表
    if filtered_users:
        # 创建用户基本信息表格
        user_basic_data = []
        for user in filtered_users:
            user_basic_data.append({
                "用户ID": user["user_id"],
                "用户名": user.get("name", f"用户{user['user_id']}"),
                "手机号": user.get("phone", "-"),
                "总调用次数": user["total_calls"]
            })
        
        df_users = pd.DataFrame(user_basic_data)
        st.dataframe(df_users, use_container_width=True, hide_index=True)
        
        # 用户详细工具调用统计
        st.subheader("📋 用户详细工具调用")
        
        # 选择用户
        selected_user = st.selectbox(
            "选择用户查看详细统计",
            options=filtered_users,
            format_func=lambda x: f"{x.get('name', f'用户{x['user_id']}')} (ID: {x['user_id']})",
            key="selected_user_detail"
        )
        
        if selected_user:
            render_user_detail_stats(selected_user)
    else:
        st.info("没有找到匹配的用户")


def render_user_detail_stats(user_data):
    """渲染用户详细统计"""
    st.write(f"**用户**: {user_data.get('name', f'用户{user_data['user_id']}')} (ID: {user_data['user_id']})")
    st.write(f"**总调用次数**: {user_data['total_calls']}")
    
    # 处理用户工具数据
    tools_data = []
    for tool_name, count in user_data["tools"].items():
        chinese_name = tool_name_map.get(tool_name, tool_name)
        tools_data.append({
            "工具名称": chinese_name,
            "调用次数": int(count)
        })
    
    # 按调用次数排序
    tools_data.sort(key=lambda x: x["调用次数"], reverse=True)
    
    # 创建两列布局
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # 显示工具调用表格
        df_tools = pd.DataFrame(tools_data)
        st.dataframe(df_tools, use_container_width=True, hide_index=True)
    
    with col2:
        # 创建柱状图
        if tools_data:
            fig = px.bar(
                x=[tool["调用次数"] for tool in tools_data[:10]],
                y=[tool["工具名称"] for tool in tools_data[:10]],
                orientation='h',
                title="工具调用次数（Top 10）"
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)

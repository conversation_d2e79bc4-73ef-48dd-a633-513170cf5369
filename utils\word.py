
from typing import IO
from docx import Document
from docx.shared import Pt
from docx.oxml.ns import qn

class WordUtil:
    def __init__(self, docx: str | IO[bytes] | None = None):
        self.doc = Document(docx)

    def _set_run_font(self, run, font_name, east_asian_font_name, font_size):
        """设置单个run的字体"""
        run.font.name = font_name
        run._element.rPr.rFonts.set(qn('w:eastAsia'), east_asian_font_name)
        if font_size:
            run.font.size = Pt(font_size)

    def _set_paragraph_font(self, paragraph, font_name, east_asian_font_name, font_size):
        """设置段落中所有run的字体"""
        for run in paragraph.runs:
            self._set_run_font(run, font_name, east_asian_font_name, font_size)

    def set_global_font(self, font_name, east_asian_font_name, font_size = None):
        """设置文档中所有文本的字体"""
        # 设置文档默认样式
        self.doc.styles['Normal'].font.name = font_name
        self.doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), east_asian_font_name)
        if font_size:
            self.doc.styles['Normal'].font.size = Pt(font_size)
        
        # 处理所有段落
        for paragraph in self.doc.paragraphs:
            self._set_paragraph_font(paragraph, font_name, east_asian_font_name, font_size)
        
        # 处理所有表格
        for table in self.doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        self._set_paragraph_font(paragraph, font_name, east_asian_font_name, font_size)
        
        # 处理页眉页脚
        for section in self.doc.sections:
            for paragraph in section.header.paragraphs:
                self._set_paragraph_font(paragraph, font_name, east_asian_font_name, font_size)
            for table in section.header.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            self._set_paragraph_font(paragraph, font_name, east_asian_font_name, font_size)
            
            for paragraph in section.footer.paragraphs:
                self._set_paragraph_font(paragraph, font_name, east_asian_font_name, font_size)
            for table in section.footer.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            self._set_paragraph_font(paragraph, font_name, east_asian_font_name, font_size)
        return self.doc

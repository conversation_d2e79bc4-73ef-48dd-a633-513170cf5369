from page.invite_code_management import render_invite_code_management
from page.user_management import render_user_management
from page.prompts_management import render_prompts_management
from page.tool_statistics import render_tool_statistics
from page.teacher_agent_management import render_teacher_agent_management
from dotenv import load_dotenv
import os
import time
from utils.redis import get_client
import redis
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from utils.mysql import get_sync_mysql
from sqlalchemy import text
import pandas as pd
import streamlit as st
# 设置页面标题 - 必须是第一个Streamlit命令
st.set_page_config(page_title="AI后台管理系统", layout="wide", menu_items=None)

# 导入页面模块
from page.user_activity import render_user_activity

# ----------------------------------------------------------------
# 启动命令：nohup streamlit run user_static.py --server.port 8501 &
# ----------------------------------------------------------------
# 加载环境变量
load_dotenv()

# 定义固定的令牌值
VALID_TOKEN = "2G9iQ4xq8uVqJbW3nH7KYhUh5OqUWiRDQPB2UmkNBV2PWG"

# 获取URL参数
token = st.query_params.get("token", "")

# 验证令牌
is_token_valid = (token == VALID_TOKEN)

# 如果令牌验证失败，只显示错误信息
if os.environ.get('APP_ENV') != "dev" and not is_token_valid:
    st.error("404 not found")
    st.stop()  # 停止脚本执行，不显示后续内容

# 添加全局样式
st.markdown("""
<style>
/* 禁用状态的按钮样式 */
button[disabled] {
    color: #666666 !important;
    opacity: 0.7;
}

/* 确保页码按钮文本可见 */
.stButton button {
    color: white !important;
}

/* 分页按钮样式 */
.stButton > button[kind="primary"] {
    background-color: #ff4b4b !important; 
    color: white !important;
    border-color: #ff4b4b !important;
}

.stButton > button[kind="secondary"] {
    background-color: #f0f2f6 !important;
    color: black !important;
    border-color: #f0f2f6 !important;
}

/* Tab 样式 */
.stTabs [data-baseweb="tab-list"] {
    gap: 8px;
}

.stTabs [data-baseweb="tab"] {
    height: 40px;
    white-space: pre-wrap;
    border-radius: 4px 4px 0px 0px;
    gap: 1px;
    padding: 10px;
}

.stTabs [aria-selected="true"] {
    background-color: #ff4b4b !important;
    color: white !important;
}

/* 隐藏侧边栏 */
[data-testid="stSidebar"] {
    display: none;
}

/* 去掉页脚Streamlit品牌标记 */
footer {
    visibility: hidden;
}
            
  .st-emotion-cache-zy6yx3{
       padding: 3rem 1rem 2rem !important; /* 修改为你需要的值 */
    }          
</style>
""", unsafe_allow_html=True)

# 创建顶部选项卡
tabs = st.tabs(["提示词管理", "用户管理", "邀请码管理", "工具统计", "用户活跃统计"])

# 根据选定的选项卡渲染对应的页面
with tabs[0]:
    render_prompts_management()

with tabs[1]:
    render_user_management()

with tabs[2]:
    render_invite_code_management()

with tabs[3]:
    render_tool_statistics()

with tabs[4]:
    render_user_activity()

# # 添加页脚
# st.divider()
# st.caption("© 2024 销售助手系统")

import os
import random
from agno.models.anthropic import Claude
from utils.model_providers.anthropic.extended_claude import ExtendedClaude

# Claude API密钥列表
CLAUDE_API_KEYS = [
    "************************************************************************************************************",
    # "************************************************************************************************************",
    "************************************************************************************************************",
    "************************************************************************************************************"
]

# Claude模型ID
# CLAUDE_MODEL_ID = "claude-3-5-haiku-20241022"

CLAUDE_MODEL_ID = "claude-3-5-haiku-latest"

# API
CLAUDE_BASE_URL = "http://ai.profly.com.cn:8699"


def get_random_claude_model(model_id=None, api_key=None, base_url=None):
    """
    获取Claude模型，可以随机选择API密钥和模型ID

    参数:
        model_id: 指定模型ID，如果为None则随机选择
        api_key: 指定API密钥，如果为None则随机选择
        base_url: 指定API基础URL，如果为None则使用默认值

    返回:
        Claude模型实例
    """
    # 如果未指定，随机选择API密钥
    if api_key is None:
        api_key = random.choice(CLAUDE_API_KEYS)

    # 如果未指定，使用默认的API基础URL
    if base_url is None:
        base_url = CLAUDE_BASE_URL

    # 设置环境变量
    os.environ["ANTHROPIC_API_KEY"] = api_key
    os.environ["ANTHROPIC_BASE_URL"] = base_url

    # 线上服务 - 缓存提示词
    if model_id is None:
        model_id = CLAUDE_MODEL_ID
        # 返回Claude模型实例
        return ExtendedClaude(
            id=model_id,
            default_headers={
                "anthropic-beta": "extended-cache-ttl-2025-04-11"},
            cache_system_prompt=True,
            extended_cache_time=True
        )

    # 测试服务 - 不缓存测试提示词
    return ExtendedClaude(
        id=model_id,
    )

# 使用示例
# model = get_random_claude_model()

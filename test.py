from ast import mod
import asyncio
import json
import re
import tempfile
from markdown import markdown
from markitdown import MarkItDown
from pypandoc.pandoc_download import download_pandoc
# see the documentation how to customize the installation path
# but be aware that you then need to include it in the `PATH`
# download_pandoc()

# 添加项目根目录到Python路径
from pathlib import Path
import sys

# rootPath = str(Path(__file__).parent)
# sys.path.append(rootPath)

import pypandoc

from orm import enterprise_business_info
from tools.attachment_tools import AttachmentTools
from tools.content_tools import small_content_writer
from tools.search_sougou import web_search
from tools.deepsearch_tools import deep_web_search_agent
from tools.baidu_search import web_search
from tools.todoist_tools import TodoistTools
from utils.ali_docmind import AliDocmind
from utils.asr.tencent_asr import flash_asr
from utils.context import set_context
from utils.converter import ai_correct_mermaid_code, mermaid_to_image
from utils.doc_parse import read_document_from_url
from md2excel import MarkdownToExcelConverter
from utils.enterprise_api import tyc_business_info
from utils.id_encode import IdEncode
from utils.models import qwen_long, gpt41
from utils.logger import logger

# TodoistTools().add_todo('test2', '2025-08-20 19:00:00', '2025-08-20 19:00:00', '2025-08-20 18:00:00')

# alternatively you could just pass some string. In this case you need to
# define the input format:
# output = pypandoc.convert_text("""# 中海安全日志助手（阳光科创中心店）会议纪要""".replace("---", ""), 'docx', format='md', outputfile="somefile.docx")
# output = pypandoc.convert_file('D:\\phpstudy_pro\\WWW\\sale-agent\\tmp\\storage\\user_files\\1\\46.md', 'docx', outputfile="somefile.docx")
# output == 'some title\r\n==========\r\n\r\n'

# 初始化工具
# attachment_tools = AttachmentTools()
# 调用工具函数
# async def run():
#     set_context("user_id", "1")
#     result = await attachment_tools.generate_document("# 这是一个测试文档\n\n一些内容", "test.docx")
#     print(result)
# import asyncio
# asyncio.run(run())

# enterprise_business_info.add_one(company_id=3, name="测试公司3", reg_number="***********", credit_code="1234567890123456789", info={"key1": "value1", "key2": "value2", "key3": "value3", "key4": "value4", "key5": "value5", "key6": "value6", "key7": "value7"}, source=1)
# print(enterprise_business_info.query_one(company_id=3))

# pypandoc.convert_text("# test\ntest", "pptx", format='md', outputfile="test.pptx")
# print(pypandoc.get_pandoc_formats())

# print(pypandoc.convert_file("C:\\Users\\<USER>\\Documents\\销售代理关键条款.docx", 'md', format='docx'))
# content = """"""
# cleaned_text = re.sub(r'^\s*---\s*$', '', content, flags=re.MULTILINE)
# print(cleaned_text)
# pypandoc.convert_text(cleaned_text, "docx", format='md', outputfile="test.docx")
# with open("D:\\Users\\Mayn\\Downloads\\test.wav", "rb") as f:
#     data = f.read()
# print(flash_asr(data, "wav"))

# sample_md = """
# # 销售目标
# | 姓名 | 手机号 | 公司 | 行业 | 主营产品 | 从业经验 | 销售目标 | 补充信息 |
# |------|--------|------|------|------------|------------|------------|-----------| 
# | zengrr | *********** | 智飞网络 | 科技 | AI agent等工具 | 1年 | 100万元 | 需要找到目标客户并精确引导他们购买产品，希望提升客户精准获取和转化能力 |
#     """
# converter = MarkdownToExcelConverter()
# print(converter._parse_markdown_tables(sample_md))
# converter.convert(sample_md, "output.xlsx")
# IdEncode = IdEncode()
# encode = "Gy2Y8DX"
# IdEncode.encode(2079)
# decode = IdEncode.decode(encode)
# print(encode, decode)

# tyc_business_info("深圳智飞网络有限公司")

# print(read_document_from_url("https://sale-agent.oss-cn-shenzhen.aliyuncs.com/test/9/2025/07/%E5%88%9B%E6%99%BA%E4%BA%91%E5%9F%8EA2%E5%85%A5%E9%A9%BB%E5%85%AC%E5%8F%B8%E5%90%8D%E5%8D%9520250702.xlsx?x-oss-credential=LTAItwkMZEO7KDat%2F20250710%2Fcn-shenzhen%2Foss%2Faliyun_v4_request&x-oss-date=20250710T072916Z&x-oss-expires=86400&x-oss-signature=d35e23a94e9ea340dff3648bedd0a561e2349a006f83e9fdd5ab4b9dd15be073&x-oss-signature-version=OSS4-HMAC-SHA256", "xlsx"))

# from pdf2docx import Converter
# cv = Converter("C:\\Users\\<USER>\\Documents\\南京芯干线科技有限公司采购订单.pdf")
# cv.convert("output.docx", start=0, end=None)
# cv.close()

# docmind = AliDocmind()
# content, chunk_list = docmind.parse_file("https://sale-agent.oss-cn-shenzhen.aliyuncs.com/test/19/2025/07/1752128337275-erhn1v77vo.xlsx?x-oss-credential=LTAItwkMZEO7KDat%2F20250710%2Fcn-shenzhen%2Foss%2Faliyun_v4_request&x-oss-date=20250710T062241Z&x-oss-expires=86400&x-oss-signature=f3e8a885ee5358c7cd9f09b839a5b56fe0daff3aedb0733db915de77b63c6338&x-oss-signature-version=OSS4-HMAC-SHA256", "xlsx")
# print(content)
# # result = docmind.get_doc_parser_result("docmind-20250708-180d6a656310475686793446a0daf057")
# # content = ""
# # if result and "layouts" in result:
# #     for item in result["layouts"]:
# #         content += item["markdownContent"]
# # print(content)
# pypandoc.convert_text(content, "docx", format='md', outputfile="test.docx")

# mermaid_code="""flowchart TD
#             A[客户接洽] --> B{需求确认}
#             B -- 需求明确 --> C[方案制定]
#             C --> D[报价]
#             D --> E{客户反馈}
#             E -- 接受报价 --> F[签约]
#             E -- 提出异议 --> G[谈判]
#             G --> D
#             B -- 需求不明确 --> H[进一步沟通]
#             H --> B
#             F --> I[项目实施]
#             I --> J[售后服务]"""
# err = asyncio.run(mermaid_to_image(
#     mermaid_code=mermaid_code,
#     output_path="diagram.png",
#     format="png"
# ))
# if err:
#     print(asyncio.run(ai_correct_mermaid_code(mermaid_code, err)))

# print(asyncio.run(deep_web_search_agent("深圳的天气怎么样")))
# print(web_search("医疗行业SaaS采购决策流程 site:.gov"))

# md = MarkItDown(enable_plugins=False) # Set to True to enable plugins
# result = md.convert("C:\\Users\\<USER>\\Documents\\客户价值分析画像.docx")
# print(result.text_content)
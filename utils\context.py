import contextvars
import threading

# 线程局部存储
thread_local = threading.local()
# 协程上下文
user_id = contextvars.ContextVar("user_id")
task_id = contextvars.ContextVar("task_id")
session_id = contextvars.ContextVar("session_id")
query = contextvars.ContextVar("query")
data = contextvars.ContextVar("data", default={})

def set_context(name, value):
    if name == "user_id":
        user_id.set(value)
    elif name == "task_id":
        task_id.set(value)
    elif name == "session_id":
        session_id.set(value)
    elif name == "query":
        query.set(value)
    else:
        data_dict = data.get()
        data_dict[name] = value
    # setattr(thread_local, name, value)

def get_context(name):
    value = None
    if name == "user_id":
        value = user_id.get()
    elif name == "task_id":
        value = task_id.get()
    elif name == "session_id":
        value = session_id.get()
    elif name == "query":
        value = query.get()
    else:
        data_dict = data.get()
        value = data_dict.get(name)
    return value
    # return getattr(thread_local, name, None)

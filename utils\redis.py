import os
from redis import Redis
from dotenv import load_dotenv



# 创建 Redis 连接池
_redis_client = None

def get_client() -> Redis:
    """
    获取 Redis 客户端实例
    :return: Redis 客户端实例
    """
    global _redis_client
    if _redis_client is None:

        # Redis 配置
        REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
        REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
        REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "123456")
        REDIS_DB = int(os.getenv("REDIS_DB", "0"))

        _redis_client = Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            db=REDIS_DB,
            decode_responses=True
        )
    return _redis_client 
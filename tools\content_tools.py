import json
import random
import string
import traceback
from agno.models.message import Message
from utils import pyapi
from utils.context import get_context
from utils.models import deepseek, deepseek_r1, deepseek_client, qwen_key, qwen_url
from utils.logger import logger
from utils.tool import push_task_chunk
from utils.redis import get_client
from utils.tool_name_map import tool_name_map
from agno.models.openai.like import OpenAILike

qwen_plus = OpenAILike(
    id="qwen-plus-latest",
    api_key=qwen_key,
    base_url=qwen_url,
    temperature=0.5,
    max_tokens=50,
)

async def small_content_writer(description: str) -> str:
    """智能内容写手
    
    Args:
        description: 内容描述、要求、参考资料等等
    Returns:
        str: 内容写手生成的内容
    """
    try:
        prompt = f"""## **任务目标**  
你是一个专为销售团队设计的智能内容与文档生成助手，能够帮助销售人员快速生成高转化率的沟通内容、专业文档及定制化方案，提升销售效率和客户体验。  

## **用户群体**  
- **一线销售代表**（电话、面谈、线上沟通）  
- **销售经理/总监**（方案策划、客户提案）  
- **商务/客户经理**（合同、报价单、标书）  
- **售前顾问**（解决方案、技术文档）  

## **核心功能**  
### **1. 销售沟通内容生成**  
- **销售话术优化**（电话脚本、微信话术、邮件模板）  
- **客户痛点分析 & 应对策略**  
- **异议处理话术**（价格、竞品对比、信任建立）  
- **跟进提醒 & 促单文案**  

### **2. 专业销售文档生成**  
| 文档类型 | 适用场景 | 示例输出 |  
|----------|----------|----------|  
| **客户提案** | 针对客户需求提供定制化方案 | 包含行业分析、解决方案、成功案例 |  
| **报价单** | 清晰呈现产品/服务价格及条款 | 支持阶梯报价、附加服务选项 |  
| **合同/协议** | 生成标准或定制化合同模板 | 涵盖服务范围、付款方式、违约责任 |  
| **解决方案** | 技术型销售或复杂产品介绍 | 包含架构图、实施流程、ROI分析 |  
| **标书/RFP响应** | 投标或正式项目申请 | 结构化响应，突出竞争优势 |  

### **3. 自动化个性化定制**  
- 支持变量替换（客户名称、公司、行业等）  
- 可调整正式度（商务正式 / 灵活口语化）  
- 支持多版本生成（简版/详版、技术型/商务型）  

## **输出标准**  
1. **专业准确**：符合行业规范，避免法律或技术错误。  
2. **结构化清晰**：标题/条款分明，便于客户快速阅读。  
3. **销售导向**：突出价值而非功能，引导客户决策。  
4. **灵活可调**：支持按反馈实时修改风格或细节。
"""
        reasoning_content = ""
        content = ""
        # response = deepseek_r1.aresponse_stream(
        #     messages=[Message(role="system", content=prompt), Message(role="user", content=description)]
        # )
        # async for chunk in response:
        #     content += chunk.content if chunk.content else ""

        task_id = get_context("task_id")
        # 从redis获取当前任务result中的知识库和网络搜索结果作为上下文
        redis_key = f"ai_task:{task_id}"
        redisClient = get_client()
        tool_result = redisClient.lrange(redis_key, 0, -1) if redisClient else []  # 获取任务结果数据
        tool_names = (tool_name_map.get("asearch_knowledge_base"), tool_name_map.get("search_knowledge_base"), tool_name_map.get("knowledge_llm_search"), tool_name_map.get("web_search"), tool_name_map.get("deep_web_search_agent"))
        tool_context = ""
        for item in tool_result:
            try:
                item = json.loads(item)
                tool = item.get("tool", {})
                if item.get("event") == "ToolCallCompleted" and tool.get("tool_name") in tool_names and tool.get("tool_result"):
                    tool_context += tool.get("tool_result", "") + "\n\n"
                    break
            except:
                pass
        user_content = description + "\n\n参考资料:\n" + tool_context
        logger.info(f"内容写手输入内容: {user_content}")
        messages = [{"role": "system", "content": prompt}, {"role": "user", "content": user_content}]
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            stream=True
        )
        for chunk in response:
            rc = ""#chunk.choices[0].delta.reasoning_content
            c = chunk.choices[0].delta.content
            if rc:
                reasoning_content += rc if rc else ""
            else:
                content += c if c else ""
            push_task_chunk(task_id=task_id, event="ToolResponse", content=rc if rc else c)

        logger.info(f"内容写手生成内容成功, 思考: {reasoning_content}, 结果: {content}")

        return content

    except Exception as e:
        logger.error(f"内容写手生成内容失败: {traceback.format_exc()}")
        return str({"status": "error", "message": f"内容写手生成内容失败: {e}"})

async def send_message_to_user(query: str = "", chat_history_num: int = 10):
    """给用户发送最终回复之前的中间消息"""
    try:
        task_id = get_context("task_id")
        session_id = get_context("session_id")
        # 生成id, 格式: call_wlUX8VhM4iAwNxyYyLuWiH5u
        tool_id = "call_" + "".join(random.choices(string.ascii_uppercase + string.ascii_lowercase + string.digits, k=24))
        tool = {
            "tool_call_id": tool_id,
            "tool_name": tool_name_map.get("send_message_to_user"),
            "tool_args": {"query": query, chat_history_num: chat_history_num},
            "tool_result": None
        }
        if chat_history_num:
            rsp_text = pyapi.call_api(f"/pyapi/chat/list?limit={chat_history_num}&session_id={session_id}&hide_tool_call=1", method="GET")
            data = json.loads(rsp_text)["data"]["list"]
            clear_context_i = chat_history_num + 1
            for i, item in enumerate(data):
                if item["content_type"] == "operation" and item["content"] == "contextClear":
                    clear_context_i = i
                    break
            chat_list = [item["content"] for item in data[:clear_context_i]]
            chat_list.reverse()
            if query: chat_list.append(query)
            last_msg = chat_list.pop() if chat_list else ""
            query = f"<历史对话>{chat_list}</历史对话>\n{last_msg}"

        push_task_chunk(task_id=task_id, event="ToolCallStarted", content="", tool=tool)
        # 请求AI流式生成反馈消息
        prompt = f"""你是一个消息确认回复助手, 任务是根据用户输入的内容生成回复给用户的确认消息, 你回复的消息必须严格遵守以下规则:
- 不要回复和引用历史对话中的消息, 历史消息仅用作理解当前用户消息的语境
- 不要和用户有任何互动, 不要试图理解用户的消息
- 回复必须仅为确认收到并表示已经开始处理了，不提供具体解决方案，不解答任何问题
- 回复的消息中不能包含任何问题或询问
- 回复的消息不能给出明确的答复、处理结果
- 回复的消息仅为通知（非阻塞，无需用户回复或确认）
- 消息回复模板(不要完全固定, 可以适当丰富内容或调整用词, 但不要偏离主旨)：
  - 无具体需求的消息: 已收到你的消息，正在为你处理中
  - 有具体需求的消息: 已收到你的消息，正在为你[处理/查询]xx[问题/需求]，稍后将为你整理展示[处理/查询]结果
        """
        content = ""
        response = qwen_plus.aresponse_stream(
            messages=[Message(role="system", content=prompt), Message(role="user", content=query)]
        )
        async for chunk in response:
            c = chunk.content if chunk.content else ""
            content += c
            if c: push_task_chunk(task_id=task_id, event="ToolResponse", content=c)
        tool["tool_result"] = content
        push_task_chunk(task_id=task_id, event="ToolCallCompleted", content="", tool=tool)
    except Exception as e:
        logger.error(f"发送中间消息失败: {traceback.format_exc()}")
        return str({"status": "error", "message": f"发送中间消息失败: {e}"})
import requests
import json
from utils.logger import logger
from utils.context import get_context
import os
from dotenv import load_dotenv
load_dotenv()

def save_user_profile(user_name: str,  user_business: str = "", user_company: str = "", user_profile: str = "") -> str:
    """
    保存当前用户(我)的信息

    Args:
        user_name: 用户名称
        user_business: 行业（科技/教育/制造/餐饮/零售/金融/医疗/房地产/旅游/其他）
        user_company: 公司名称
        user_profile: 自然语言总结（主营产品、从业经验、销售目标、补充信息等）

    Returns:
        str: 包含操作结果的JSON字符串
    """
    try:
        data: dict[str, str] = {
            "name": user_name,
            "business_type": user_business,
            "company": user_company,
            "profile": user_profile,
            "user_id": get_context("user_id"),
        }
        logger.info(f"保存用户画像:{data}")
        response = requests.post(
            f"{os.getenv('API_URL')}/pyapi/user/profile", data=data)
        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(f"保存用户画像失败:{str(response.text)}")
            return json.dumps({"error": "保存用户信息失败."}, ensure_ascii=False)
    except Exception as e:
        logger.error(f"保存用户画像失败: {str(e)}")
        logger.error(f"保存用户画像失败:{str(response.text)}")
        result = {
            "success": False,
            "message": f"保存用户信息失败"
        }
        return json.dumps(result, ensure_ascii=False)


def update_user_profile(user_name: str = "",  user_business: str = "", user_company: str = "", user_profile: str = "") -> str:
    """
    用于更新用户(我)的基本信息和AI沟通偏好，形成简明的用户画像

    Args:
        user_name: 用户名称
        user_business: 行业（科技/教育/制造/餐饮/零售/金融/医疗/房地产/旅游/其他）
        user_company: 公司名称
        user_profile: 简要总结用户的业务背景、主营产品、经验、目标等信息，可补充用户希望AI采用的沟通风格或输出偏好

    Returns:
        str: 包含操作结果的JSON字符串
    """
    try:
        data: dict[str, str] = {
            "name": user_name,
            "business_type": user_business,
            "company": user_company,
            "profile": user_profile,
            "user_id": get_context("user_id"),
        }
        logger.info(f"编辑用户信息:{data}")
        response = requests.post(
            f"{os.getenv('API_URL')}/pyapi/user/editProfile", data=data)
        if response.status_code == 200:
            return json.dumps(response.json(), ensure_ascii=False)
        else:
            logger.error(f"编辑用户信息失败:{str(response.text)}")
            return json.dumps({"error": "编辑用户信息失败."}, ensure_ascii=False)
    except Exception as e:
        logger.error(f"编辑用户信息失败: {str(e)}")
        logger.error(f"编辑用户信息失败:{str(response.text)}")
        result = {
            "success": False,
            "message": f"编辑用户信息失败"
        }
        return json.dumps(result, ensure_ascii=False)

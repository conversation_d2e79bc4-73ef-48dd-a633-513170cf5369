from agno.agent import Agent
from agno.models.openai import OpenAIChat
from utils.storage import get_singlestore_storage
from tools.customer_tools import CustomerTools
import logging
from agno.models.deepseek import DeepSeek
from agno.models.openai.like import OpenAILike
from utils.models import mainModel
import copy

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("customer_agent")

# 获取SingleStore存储实例
storage = get_singlestore_storage(table_name="customer_sessions")


# 创建客户管理智能体
customer_agent = Agent(
    name="客户信息助手",
    agent_id="002",
    model=mainModel,
    # storage=storage,
    tools=[copy.deepcopy(CustomerTools())],
    reasoning=True,
    markdown=False,
    # show_tool_calls=True,  # 显示工具调用
    debug_mode=True,  # 启用调试模式
    add_datetime_to_instructions=True,
    user_id="harris100",  # 默认值，会在API调用时被替换
    session_id="harris100",  # 默认值，会在API调用时被替换
    create_default_user_message=False,
    instructions="""
你是一个专业的客户信息管理助手。

核心职责：
- 添加新的客户信息
- 更新现有客户的详细信息
- 记录客户沟通历史
- 管理客户联系方式
- 跟踪客户需求和偏好
- 查看客户互动历史

可用工具：
- add_customer: 添加新客户
- update_customer: 更新客户信息
- bind_busi: 商机和客户关联
- get_customer: 获取客户详情
- delete_customer: 删除客户（谨慎使用）
- list_customers: 列出客户
- add_interaction: 添加互动记录
- get_interactions: 获取互动历史

规则与注意事项：
- 互动记录管理：记录每次与客户的重要互动，包含互动类型、内容和后续行动，保持时间记录的准确性
- 如果工具执行结果有link链接地址，需要返回输出链接地址，方便让用户可以点击查看链接
"""
)

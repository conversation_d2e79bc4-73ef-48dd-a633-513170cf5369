# 扩展的Claude客户端

这个扩展实现允许你通过继承来自定义Claude的`_should_retry`方法，而不是直接修改底层库的代码。

## 文件结构

- `claude_client.py`: 包含`ExtendedAnthropicClient`类，它继承自`Anthropic`并提供了自定义的重试逻辑
- `extended_claude.py`: 包含`ExtendedClaude`类，它继承自`agno.models.anthropic.Claude`并使用扩展的Anthropic客户端
- `claude_example.py`: 提供了使用扩展Claude类的示例代码

## 功能特点

### 增强的重试逻辑

`ExtendedAnthropicClient`类提供了以下增强功能：

1. **自定义重试条件**:
   - 对连接错误和超时错误进行重试
   - 对速率限制错误进行重试
   - 对特定HTTP状态码进行重试
   - 根据响应内容决定是否重试

## 使用方法

### 基本用法

```python
from utils.extended_claude import ExtendedClaude
from agno.models.base import Message

# 创建扩展的Claude实例
claude = ExtendedClaude(
    id="claude-3-5-sonnet-20241022",  # 或其他Claude模型ID
)

# 创建消息
messages = [
    Message(role="user", content="Hello, how are you?")
]

# 使用扩展的Claude实例发送消息
response = claude.invoke(messages)
```

### 在现有代码中替换Claude

如果你已经在使用`agno.models.anthropic.Claude`，你可以简单地将导入语句从：

```python
from agno.models.anthropic import Claude
```

替换为：

```python
from utils.extended_claude import ExtendedClaude as Claude
```

这样，你的代码将使用扩展的Claude类，而不需要其他更改。

## 自定义重试逻辑

如果你想进一步自定义重试逻辑，你可以修改`claude_client.py`文件中的`_should_retry`方法。

### 自定义重试条件

在`_should_retry`方法中，你可以添加自己的条件来决定是否重试请求：

```python
def _should_retry( self, response: httpx.Response) -> bool:
    # 首先调用父类的_should_retry方法
    result = super()._should_retry(response=response)

    # 如果父类决定不重试，我们可以添加自己的重试逻辑
    if not should_retry:
        # 添加你自己的重试条件
        if your_custom_condition:
            return True

    return should_retry
```

## 注意事项

- 确保在使用扩展Claude类之前设置了必要的环境变量，如`ANTHROPIC_API_KEY`
- 过度重试可能会导致API使用费用增加，请谨慎设置重试参数
- 对于异步操作，目前仍使用原始的`AsyncAnthropicClient`，如果需要，你可以创建一个扩展的异步客户端
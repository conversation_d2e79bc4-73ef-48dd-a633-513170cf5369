import os
import tempfile
import traceback
from playwright.sync_api import sync_playwright
from tools.attachment_tools import upload_file
from utils.converter import ai_correct_mermaid_code, mermaid_to_image
from utils.logger import logger

async def generate_mermaid_chart(mermaid_code: str, name: str, format: str = "png", theme: str = "default", width: int = 2560, hight: int = 1440):
    """
    通过Mermaid代码生成图表
    
    :param mermaid_code: Mermaid语法代码
    :param name: 文件名
    :param format: 输出格式（png）
    :param theme: Mermaid主题（default/dark/forest等）
    :param width: 图片分辨率宽度, 默认2560
    :param hight: 图片分辨率高度, 默认1440
    """
    try:
        with tempfile.NamedTemporaryFile(suffix=f".{format}", delete=False) as tmp_file:
            output_path = tmp_file.name
            err = ""
            first_err = ""
            retry_count = 2
            for i in range(retry_count + 1):
                err = await mermaid_to_image(mermaid_code, output_path, format, theme, width, hight)
                if err:
                    if i == 0: first_err = err
                    if i < retry_count:
                        logger.warning(f"mermaid代码语法错误, 第{i+1}次修正尝试: {err}")
                        mermaid_code = await ai_correct_mermaid_code(mermaid_code, err)
                else:
                    break
            else:
                if err:
                    return str({"status": "error", "message": first_err})
            return upload_file(file_path=output_path, name=name, content_type=format)
    except Exception as e:
        err = f"生成mermaid图表失败: "
        logger.error(err + traceback.format_exc())
        return str({"status": "error", "message": err + str(e)})
    finally:
        if os.path.exists(output_path):
            os.unlink(output_path)

from typing import AsyncIterator, Iterator, List

from agno.document import Document
from agno.knowledge.agent import AgentKnowledge


class DocumentKnowledgeBase(AgentKnowledge):
    """自定义文档知识库类, 需要自己将文档读取出来并切分好后的文档列表传进来作为写入向量库的文档, 由于官方的类没有实现异步迭代器导致无法实现异步写入, 所以自己覆写了带异步迭代器的类"""
    documents: List[Document]

    @property
    def document_lists(self) -> Iterator[List[Document]]:
        """Iterate over documents and yield lists of documents.
        Each object yielded by the iterator is a list of documents.

        Returns:
            Iterator[List[Document]]: Iterator yielding list of documents
        """

        for _document in self.documents:
            yield [_document]

    @property
    async def async_document_lists(self) -> AsyncIterator[List[Document]]:
        """Iterate over documents and yield lists of documents.
        Each object yielded by the iterator is a list of documents.

        Returns:
            AsyncIterator[List[Document]]: Iterator yielding list of documents
        """

        for _document in self.documents:
            yield [_document]

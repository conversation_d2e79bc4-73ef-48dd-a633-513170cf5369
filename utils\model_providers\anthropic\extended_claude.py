from agno.models.anthropic import <PERSON>
from typing import Dict, Any, Optional, List, Union, AsyncIterator
from .claude_client import ExtendedAnthropicClient, ExtendedAsyncAnthropicClient
from anthropic import AsyncAnthropic as AsyncAnthropicClient
import asyncio
from utils.logger import logger
import time
import random
import traceback
from anthropic.types import MessageStreamEvent


class ExtendedClaude(<PERSON>):
    """
    扩展的Claude类，使用自定义的Anthropic客户端
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._extended_client: Optional[ExtendedAnthropicClient] = None
        self._extended_async_client: Optional[ExtendedAsyncAnthropicClient] = None

    def get_client(self) -> ExtendedAnthropicClient:
        """
        返回扩展的Anthropic客户端实例
        """
        if self._extended_client:
            return self._extended_client

        _client_params = self._get_client_params()
        self._extended_client = ExtendedAnthropicClient(**_client_params)
        return self._extended_client

    def get_async_client(self) -> ExtendedAsyncAnthropicClient:
        """
        返回异步Anthropic客户端实例
        使用扩展的ExtendedAsyncAnthropicClient来支持自定义重试逻辑
        """
        if self._extended_async_client:
            return self._extended_async_client

        _client_params = self._get_client_params()
        self._extended_async_client = ExtendedAsyncAnthropicClient(
            **_client_params)
        return self._extended_async_client

    async def ainvoke_stream(self, *args, **kwargs) -> AsyncIterator[MessageStreamEvent]:
        """
        重写ainvoke_stream方法，添加自定义错误处理和重试逻辑
        """
        max_retries = 5
        retry_count = 0
        base_delay = 1  # 初始延迟1秒
        max_delay = 30  # 最大延迟30秒

        while True:
            try:
                # 调用原始方法获取异步生成器
                stream_generator = super().ainvoke_stream(*args, **kwargs)
                
                # 正确处理异步生成器：迭代并转发每个chunk
                async for chunk in stream_generator:
                    yield chunk
                
                # 如果成功完成，跳出循环
                break
                
            except Exception as e:
                error_message = str(e)
                logger.error(f"Claude流式请求出错: {error_message}")
                logger.error(traceback.format_exc())
                
                # 检查是否是可重试的错误
                if "overloaded_error" in error_message or "rate_limit_error" in error_message or "server_error" in error_message:
                    retry_count += 1
                    
                    if retry_count <= max_retries:
                        # 计算指数退避延迟时间
                        delay = min(max_delay, base_delay * (2 ** (retry_count - 1)))
                        # 添加随机抖动
                        jitter = delay * 0.1 * (0.5 - random.random())
                        delay = max(1, delay + jitter)
                        
                        logger.warning(f"检测到可重试错误，将在{delay:.2f}秒后进行第{retry_count}次重试")
                        await asyncio.sleep(delay)
                        continue
                    else:
                        logger.error(f"达到最大重试次数({max_retries})，放弃重试")
                
                # 不可重试或超过重试次数，重新抛出异常
                raise

    def get_system_message_for_model(self, tools: Optional[List[Any]] = None) -> Optional[str]:
        if tools is not None and len(tools) > 0:
            tool_call_prompt = ""
            return tool_call_prompt
        return None



from datetime import datetime
from hashlib import md5
from os import getenv
import re
from dotenv import load_dotenv
import alibabacloud_oss_v2 as oss  # 导入阿里云OSS V2 SDK

from utils.logger import logger

load_dotenv()

# 从环境变量中加载凭证信息，用于身份验证
credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()

# 加载SDK的默认配置，并设置凭证提供者
oss_cfg = oss.config.load_default()
oss_cfg.credentials_provider = credentials_provider
# 设置配置中的区域信息
oss_cfg.region = getenv("OSS_REGION")
oss_cfg.endpoint = getenv("OSS_ENDPOINT")

# 使用配置好的信息创建OSS客户端
oss_client = oss.Client(oss_cfg)

def upload_file(user_id, file_path, file_type: str, file_name: str = ""):
    """
    上传文件到OSS。

    :param user_id: 用户ID，用于构建文件存储路径。
    :param file_path: 本地文件的路径。
    :param file_type: 文件后缀名。
    :return: 上传成功返回在OSS中存储的对象名称，否则返回False。
    """
    # 创建一个用于上传文件的对象
    uploader = oss_client.uploader()

    # 计算文件的 MD5 哈希值
    hash_md5 = md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    file_hash = hash_md5.hexdigest()

    # 调用方法执行文件上传操作
    dir = f"{getenv('APP_ENV', 'test')}/{user_id}/{datetime.now().year}/{datetime.now().strftime('%m')}"
    # 判断文件名不带后缀时使用文件类型作为后缀名
    if file_name:
        # 定义非法字符集合：空格、引号、斜杠、反斜杠、冒号、星号、问号、尖括号、竖线等
        illegal_chars = r' \'"\\/:*?<>|'
        # 创建正则表达式模式，匹配任何非法字符
        pattern = f'[{re.escape(illegal_chars)}]'
        # 去除非法字符
        file_name = re.sub(pattern, '-', file_name)
        object = f"{dir}/{file_name}"
    else:
        object = f"{dir}/{file_hash}.{file_type}"
    # 生成文件hash值作为文件名
    result = uploader.upload_file(
        oss.PutObjectRequest(
            bucket=getenv("OSS_BUCKET"),  # 指定目标存储空间
            key=object,        # 指定文件在OSS中的名称
        ),
        filepath=file_path  # 指定本地文件的位置
    )
    # 检查上传是否成功
    if result.status_code == 200:
        return object
    else:
        logger.error(f"文件上传失败，错误码: {result.status_code}, {result.status}")
        return False
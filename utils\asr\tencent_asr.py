# -*- coding: utf-8 -*-

import os
import time
import sys
import threading
import hashlib
import json
from datetime import datetime, timedelta
from pathlib import Path
from dotenv import load_dotenv
from tencentcloud.common import credential
from utils.asr import flash_recognizer
from utils.diskcache import gen_cache_hash_key, load_from_cache, save_to_cache
from utils.logger import logger

load_dotenv()

# 注意：使用前务必先填写APPID、SECRET_ID、SECRET_KEY，否则会无法运行！！！
APPID = os.getenv("TENCENT_APPID")
SECRET_ID = os.getenv("TENCENT_SECRET_ID")
SECRET_KEY = os.getenv("TENCENT_SECRET_KEY")
ENGINE_TYPE = "16k_zh-PY"

credential_var = credential.Credential(SECRET_ID, SECRET_KEY)
# 新建FlashRecognizer，一个recognizer可以执行N次识别请求
recognizer = flash_recognizer.FlashR<PERSON>ognizer(APPID, credential_var)

def flash_asr(file_data: bytes, voice_format: str, engine_type: str = ENGINE_TYPE):
    try:
        # 检查缓存
        cache_key = gen_cache_hash_key(file_data)
        resp = load_from_cache(cache_key)
        if not resp:
            # 校验文件大小
            if len(file_data) > 100 * 1024 * 1024:
                raise Exception("asr recognize faild! file size is too large, max size is 100MB")
            # 新建识别请求
            req = flash_recognizer.FlashRecognitionRequest(engine_type)
            req.set_filter_modal(0)
            req.set_filter_punc(0)
            req.set_filter_dirty(0)
            req.set_voice_format(voice_format)
            req.set_word_info(0)
            req.set_convert_num_mode(1)

            #执行识别
            response = recognizer.recognize(req, file_data)
            response.raise_for_status()
            resp = response.json()
            request_id = resp["request_id"]
            code = resp["code"]
            if code != 0:
                raise Exception(f"request_id: {request_id}, code: {code}, message: {resp["message"]}")
            # 保存到缓存
            save_to_cache(cache_key, resp, expire=3600 * 24 * 7)
        else:
            logger.info(f"asr recognize from cache(key={cache_key})")

        #一个channl_result对应一个声道的识别结果
        #大多数音频是单声道，对应一个channl_result
        result = ""
        for channl_result in resp["flash_result"]:
            result += channl_result["text"] + "\n"

        return result
    except Exception as e:
        logger.error("asr recognize faild: " + str(e))
        raise e
from agents.todo_agent import todo_agent
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("example_usage")

# 初始对话
user_input = "创建一个待办事项：明天下午3点和客户A开会"
response = todo_agent.chat(user_input)
print(f"智能体回复：\n{response}\n")

# 假设第二次尝试添加相似任务
user_input_2 = "添加一个任务：明天下午和客户A讨论合同"
response_2 = todo_agent.chat(user_input_2)
print(f"智能体回复：\n{response_2}\n")

# 用户确认要强制添加
user_input_3 = "我确认要添加这个任务，即使有冲突"
response_3 = todo_agent.chat(user_input_3)
print(f"智能体回复：\n{response_3}\n") 
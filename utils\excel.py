import re
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter

class ExcelConverter:
    def __init__(self, width: int = 20, height: int = 20, header_font: Font = None, cell_font: Font = None, alignment: Alignment = None, border: Border = None, add_title_row: bool = True):
        """
        初始化转换器

        :param width: 列宽
        :param height: 行高
        :param header_font: 表头字体样式
        :param cell_font: 单元格字体样式
        :param alignment: 单元格对齐方式
        :param border: 单元格边框
        :param add_title_row: 是否为每个工作表添加标题行
        """
        self.wb = Workbook()
        self.default_sheet = self.wb.active
        self.default_sheet.title = "Sheet1"

        # 定义基础样式
        self.width = width
        self.height = height
        self.add_title_row = add_title_row
        self.header_font = Font(bold=True, size=12, name="宋体") if header_font is None else header_font
        self.cell_font = Font(size=11, name="宋体") if cell_font is None else cell_font
        # 标题行字体：比表头字体大3个点，加粗
        self.title_font = Font(bold=True, size=self.header_font.size + 3, name="宋体") if self.header_font.size else Font(bold=True, size=15, name="宋体")
        self.alignment = Alignment(horizontal='center', vertical='center') if alignment is None else alignment
        self.border = Border(left=Side(style='thin'),
                            right=Side(style='thin'),
                            top=Side(style='thin'),
                            bottom=Side(style='thin')) if border is None else border

    def parse_markdown_tables(self, md_content):
        """解析Markdown中的表格，返回标题和表格数据"""
        # 使用正则表达式匹配markdown表格
        table_pattern = r'[ ]*(\|.*\|)[ ]*\n[ ]*\|([ \-:|]*)\|[ ]*\n((?:[ ]*\|.*\|[ ]*\n)*)'
        tables = []
        md_content += "\n" # 追加一个换行防止最后一行数据提取不到
        
        for i, match in enumerate(re.finditer(table_pattern, md_content)):
            header_row, separator, data_rows = match.groups()
            
            # 获取最近的任意级别标题
            # 获取所有标题及其结束位置
            headers = []
            for h in re.finditer(r'^(#{1,6})\s*(.*?)\s*$', md_content, flags=re.MULTILINE):
                end_pos = h.end()
                if end_pos < match.start():
                    headers.append({
                        'text': h.group(2).strip(),
                        'end_pos': end_pos
                    })
            
            # 找到最近的标题
            closest_header = next(iter(reversed(headers)), None)
            title = closest_header['text'] if closest_header else f'Table {i+1}'
            title = re.sub(r'[^\w\u4e00-\u9fa5]', '', title[:30])
            
            # 处理表头
            header = [cell.strip() for cell in header_row.split('|')[1:-1]]
            
            # 处理数据行
            data = []
            for row in data_rows.split('\n'):
                if row.strip():
                    cells = [cell.strip() for cell in row.split('|')[1:-1]]
                    data.append(cells)
            
            if len(header) > 0 and len(data) > 0:
                tables.append({
                    'title': re.sub(r'[\\/:*?\[\]]', '', title)[:30],
                    'header': header,
                    'data': data
                })
        
        return tables

    def _add_title_row(self, sheet, title, col_count):
        """
        为工作表添加标题行

        :param sheet: 工作表对象
        :param title: 标题文本
        :param col_count: 列数（用于合并单元格）
        """
        # 直接在第一行添加标题（此时sheet应该是空的）
        # 创建标题行数据
        title_row = [title] + [''] * (col_count - 1)
        sheet.append(title_row)

        # 合并标题行的所有单元格（从第1列到第col_count列）
        if col_count > 1:
            start_cell = f"A1"
            end_cell = f"{get_column_letter(col_count)}1"
            sheet.merge_cells(f"{start_cell}:{end_cell}")

    def _apply_styles(self, sheet, max_col):
        """应用样式到工作表"""
        # 设置列宽和行高
        for col in range(1, max_col+1):
            column_letter = get_column_letter(col)
            sheet.column_dimensions[column_letter].width = self.width

        # 根据是否有标题行来设置行高
        if self.add_title_row:
            # 标题行高度：比表头行高增加20-30%
            title_row_height = int(self.height * 1.25) + 5
            sheet.row_dimensions[1].height = title_row_height
            # 表头行高度
            sheet.row_dimensions[2].height = self.height + 5
            # 数据行高度
            for row in range(3, sheet.max_row + 1):
                sheet.row_dimensions[row].height = self.height
        else:
            # 原有逻辑：表头行高度
            sheet.row_dimensions[1].height = self.height + 5
            for row in range(2, sheet.max_row + 1):
                sheet.row_dimensions[row].height = self.height

        # 应用全局样式
        for row in sheet.iter_rows():
            for cell in row:
                cell.font = self.cell_font
                cell.alignment = self.alignment
                cell.border = self.border

        # 应用特殊样式
        if self.add_title_row:
            # 标题行样式（第1行）
            for cell in sheet[1]:
                cell.font = self.title_font
                cell.alignment = Alignment(horizontal='center', vertical='center')
            # 表头样式（第2行）
            for cell in sheet[2]:
                cell.font = self.header_font
        else:
            # 表头样式（第1行）
            for cell in sheet[1]:
                cell.font = self.header_font

    def fill_tables(self, tables: list[dict]):
        """填充数据到工作表"""
        # 删除默认创建的空sheet
        if len(self.wb.worksheets) > 0 and tables:
            if self.wb.worksheets[0].max_row == 0:
                self.wb.remove(self.wb.worksheets[0])

        # 为每个表格创建sheet
        for i, table in enumerate(tables):
            if i == 0:
                sheet = self.wb.active
                sheet.title = table['title'][:30]
            else:
                sheet = self.wb.create_sheet(title=table['title'][:30])

            # 获取列数
            col_count = len(table['header'])

            if self.add_title_row:
                # 添加标题行
                self._add_title_row(sheet, table['title'], col_count)
                # 写入表头
                sheet.append(table['header'])
            else:
                # 写入表头
                sheet.append(table['header'])

            # 写入数据
            for row in table['data']:
                sheet.append(row)

            # 应用样式
            self._apply_styles(sheet, col_count)
        return self.wb
            

    def md_to_excel(self, md_content, output_path = None):
        """转换入口函数"""
        tables = self.parse_markdown_tables(md_content)
        self.fill_tables(tables)
        
        # 保存文件
        if output_path:
            self.wb.save(output_path)
        return self.wb

